<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A855F7;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="highlight" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="512" cy="512" r="480" fill="url(#glassGradient)"/>
  
  <!-- Glass highlight -->
  <circle cx="512" cy="512" r="480" fill="url(#highlight)"/>
  
  <!-- Music note icon -->
  <g transform="translate(512, 512)">
    <!-- Note stem -->
    <rect x="80" y="-200" width="24" height="280" fill="#FFFFFF" rx="12"/>
    
    <!-- Note head -->
    <ellipse cx="92" cy="80" rx="40" ry="28" fill="#FFFFFF"/>
    
    <!-- Musical staff lines -->
    <line x1="-150" y1="-80" x2="150" y2="-80" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="-40" x2="150" y2="-40" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="0" x2="150" y2="0" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="40" x2="150" y2="40" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="80" x2="150" y2="80" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    
    <!-- Sound waves -->
    <path d="M 140 -40 Q 180 -20 140 0 Q 180 20 140 40" 
          stroke="#FFFFFF" stroke-width="12" fill="none" opacity="0.7" stroke-linecap="round"/>
    <path d="M 160 -60 Q 220 -30 160 0 Q 220 30 160 60" 
          stroke="#FFFFFF" stroke-width="12" fill="none" opacity="0.5" stroke-linecap="round"/>
  </g>
  
  <!-- Glass reflection -->
  <ellipse cx="400" cy="300" rx="120" ry="200" fill="#FFFFFF" opacity="0.15" transform="rotate(-30 400 300)"/>
</svg>
