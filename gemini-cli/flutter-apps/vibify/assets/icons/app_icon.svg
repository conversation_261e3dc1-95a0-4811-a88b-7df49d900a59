<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Glass gradient -->
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A855F7;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glass highlight -->
    <linearGradient id="highlight" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0.1" />
    </linearGradient>
    
    <!-- Shadow -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Glass blur -->
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2"/>
    </filter>
  </defs>
  
  <!-- Background circle with glass effect -->
  <circle cx="512" cy="512" r="480" fill="url(#glassGradient)" filter="url(#shadow)"/>
  
  <!-- Glass highlight overlay -->
  <circle cx="512" cy="512" r="480" fill="url(#highlight)"/>
  
  <!-- Music note icon -->
  <g transform="translate(512, 512)">
    <!-- Main note stem -->
    <rect x="80" y="-200" width="24" height="280" fill="#FFFFFF" rx="12"/>
    
    <!-- Note head -->
    <ellipse cx="92" cy="80" rx="40" ry="28" fill="#FFFFFF"/>
    
    <!-- Musical staff lines (decorative) -->
    <line x1="-150" y1="-80" x2="150" y2="-80" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="-40" x2="150" y2="-40" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="0" x2="150" y2="0" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="40" x2="150" y2="40" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="80" x2="150" y2="80" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    
    <!-- Treble clef (stylized) -->
    <path d="M -120 -60 Q -100 -100 -80 -60 Q -60 -20 -80 20 Q -100 60 -120 20 Q -140 -20 -120 -60 Z" 
          fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Sound waves -->
    <path d="M 140 -40 Q 180 -20 140 0 Q 180 20 140 40" 
          stroke="#FFFFFF" stroke-width="12" fill="none" opacity="0.7" stroke-linecap="round"/>
    <path d="M 160 -60 Q 220 -30 160 0 Q 220 30 160 60" 
          stroke="#FFFFFF" stroke-width="12" fill="none" opacity="0.5" stroke-linecap="round"/>
  </g>
  
  <!-- Glass reflection -->
  <ellipse cx="400" cy="300" rx="120" ry="200" fill="#FFFFFF" opacity="0.15" transform="rotate(-30 400 300)"/>
</svg>
