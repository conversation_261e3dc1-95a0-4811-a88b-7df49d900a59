<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="splashGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#A855F7;stop-opacity:0.9" />
      <stop offset="70%" style="stop-color:#7C3AED;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#6D28D9;stop-opacity:1" />
    </linearGradient>
    
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4"/>
      <feColorMatrix values="1 0 1 0 0  0 1 1 0 0  1 0 1 0 0  0 0 0 1 0"/>
    </filter>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="32" y="32" width="448" height="448" rx="80" fill="url(#splashGradient)"/>
  
  <!-- Glow effect -->
  <rect x="32" y="32" width="448" height="448" rx="80" fill="url(#splashGradient)" filter="url(#glow)" opacity="0.5"/>
  
  <!-- Music note -->
  <g transform="translate(256, 256)">
    <!-- Note stem -->
    <rect x="40" y="-100" width="16" height="140" fill="#FFFFFF" rx="8"/>
    
    <!-- Note head -->
    <ellipse cx="48" cy="40" rx="24" ry="16" fill="#FFFFFF"/>
    
    <!-- Decorative elements -->
    <circle cx="-60" cy="-40" r="8" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="-80" cy="0" r="6" fill="#FFFFFF" opacity="0.4"/>
    <circle cx="-70" cy="40" r="4" fill="#FFFFFF" opacity="0.3"/>
    
    <circle cx="100" cy="-60" r="6" fill="#FFFFFF" opacity="0.5"/>
    <circle cx="120" cy="-20" r="8" fill="#FFFFFF" opacity="0.4"/>
    <circle cx="110" cy="20" r="4" fill="#FFFFFF" opacity="0.3"/>
  </g>
</svg>
