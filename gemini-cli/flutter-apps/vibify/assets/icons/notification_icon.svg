<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="notifGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="128" cy="128" r="120" fill="url(#notifGradient)"/>
  
  <!-- Music note -->
  <g transform="translate(128, 128)">
    <rect x="20" y="-50" width="8" height="70" fill="#FFFFFF" rx="4"/>
    <ellipse cx="24" cy="20" rx="12" ry="8" fill="#FFFFFF"/>
    
    <!-- Sound wave -->
    <path d="M 40 -10 Q 60 0 40 10" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-linecap="round"/>
  </g>
</svg>
