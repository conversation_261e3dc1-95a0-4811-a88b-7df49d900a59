form: Flutter (Cross-platform - Android & iOS)
	•	Theme: Liquid Glass UI across all components
	•	Design Adaptability: Fully responsive for Android and iOS (Material & Cupertino adaptation)
	•	Overflow Handling: Fix all size, padding, margin, and alignment issues using LayoutBuilder, Flexible, Expanded, MediaQuery, SafeArea, etc.

⸻

2. 🧩 Core Features

A. Music Playback
	•	Play music from:
	•	Local files
	•	YouTube (streaming)
	•	Support formats: .mp3, .aac, .wav, .m4a, etc.

B. YouTube Integration
	•	Search YouTube for music/videos
	•	Play YouTube audio in the background (convert video to audio)
	•	Use youtube_explode_dart or flutter_youtube_api for fetching streams
	•	Extract audio stream only, suppress video UI (to stay audio-only)

C. Offline Download
	•	Download and store audio from YouTube to local storage
	•	Maintain metadata (Title, Artist, Album Art)
	•	Store in user-accessible directory
	•	Show download progress, success/failure messages

D. Music Library
	•	Tabs: All Songs, Favorites, Downloads, Playlists
	•	Sort/filter options
	•	Add to Favorites
	•	Custom Playlists (create/edit/delete)

E. Player Screen
	•	Full Liquid Glass UI with:
	•	Track info (title, artist, album)
	•	Album art (with blur/glass background)
	•	Playback controls (Play/Pause, Next, Prev, Shuffle, Repeat)
	•	Seek bar with wave form/progress animation
	•	Animated visualizer (optional)

⸻

3. 🎨 UI/UX - Liquid Glass Theme

A. Global Theme
	•	Use BackdropFilter for frosted glass effect
	•	Glassmorphism style with:
	•	Blur filters
	•	Translucent containers
	•	Gradient overlays
	•	Soft glowing icons/text
	•	Match system dark/light mode

B. Components
	•	Custom AppBar, Bottom NavBar, Dialogs, Sliders – all with glass effect
	•	Reusable liquid glass widgets (create GlassContainer)

C. Responsiveness
	•	Adapt to all screen sizes and orientations
	•	Use MediaQuery, FractionallySizedBox, and Wrap as needed
	•	Avoid Overflow using Expanded, Flexible, SingleChildScrollView, etc.

⸻

4. ⚙️ Technical Stack

A. Flutter Packages
	•	Audio Playback: just_audio, audioplayers, assets_audio_player
	•	YouTube Streaming: youtube_explode_dart, flutter_youtube_view
	•	File Downloads: dio, flutter_downloader
	•	Storage: path_provider, permission_handler, shared_preferences, hive
	•	UI: glassmorphism_ui, flutter_blurhash, lottie, animations
	•	State Management: provider, riverpod, or bloc
	•	Background Task: flutter_background_service, audio_service

B. Permissions
	•	Storage Access (for downloads)
	•	Background Mode (for music play)
	•	Internet Access (for YouTube & streaming)
	•	Notifications (media controls)

⸻

5. 🧠 Smart Features (Optional)
	•	Lyrics Fetch & Display (e.g., from Musixmatch API)
	•	Suggested Songs/Trending via YouTube API
	•	Theme Customization (User can pick gradients/colors)
	•	Sleep Timer
	•	Lock Screen Controls

⸻

6. 🔁 Navigation Flow
SplashScreen → Onboarding → HomePage (Tabs)
                           ↳ PlayerScreen
                           ↳ SearchYouTube
                           ↳ DownloadedSongs
                           ↳ Settings

7. 🔧 Settings Page
	•	Dark/Light Mode Toggle
	•	Clear Cache
	•	Storage Info
	•	Playback Settings
	•	App Info / About / Licenses

⸻

8. ✅ Testing Checklist
	•	✅ Overflow handling on small and large screens
	•	✅ Liquid Glass theme consistent across all widgets
	•	✅ Offline play of downloaded YouTube music
	•	✅ Smooth transition between states (loading, playing, error)
	•	✅ Responsive design on Android & iOS devices

⸻

9. 📂 Project Structure
lib/
├── main.dart
├── ui/
│   ├── screens/
│   ├── widgets/
│   └── theme/
├── models/
├── services/
├── controllers/
└── utils/



10. 🚀 Build + Deployment
	•	Support both Android and iOS builds
	•	Generate signed APK / IPA
	•	Test on emulators and real devices
	•	Use flutter build apk --release and flutter build ios