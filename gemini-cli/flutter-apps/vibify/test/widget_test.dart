// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:vibify/main.dart';

void main() {
  testWidgets('Vibify app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VibifyApp());

    // Verify that the app loads with the welcome message.
    expect(find.text('Welcome to Vibify'), findsOneWidget);
    expect(
      find.text('Your music streaming app with Liquid Glass UI'),
      findsOneWidget,
    );
    expect(find.byIcon(Icons.music_note), findsOneWidget);
  });
}
