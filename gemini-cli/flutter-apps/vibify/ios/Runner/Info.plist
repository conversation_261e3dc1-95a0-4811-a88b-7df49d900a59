<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Vibify</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>vibify</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Vibify App Permissions and Configurations -->

	<!-- Background audio playback -->
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>background-processing</string>
	</array>

	<!-- Network usage description -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>

	<!-- Photo library access for album art -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Vibify needs access to your photo library to display custom album artwork.</string>

	<!-- Microphone access (if needed for audio processing) -->
	<key>NSMicrophoneUsageDescription</key>
	<string>Vibify may need microphone access for audio processing features.</string>

	<!-- File access for local music files -->
	<key>NSDocumentsFolderUsageDescription</key>
	<string>Vibify needs access to your documents to play local music files.</string>

	<!-- Music library access -->
	<key>NSAppleMusicUsageDescription</key>
	<string>Vibify can integrate with your Apple Music library for a better experience.</string>

	<!-- Media library access -->
	<key>kTCCServiceMediaLibrary</key>
	<string>Vibify needs access to your media library to play your music.</string>

</dict>
</plist>
