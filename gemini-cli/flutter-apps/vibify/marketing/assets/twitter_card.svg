<svg width="1200" height="675" viewBox="0 0 1200 675" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="twitterBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="675" fill="url(#twitterBg)"/>
  
  <!-- Left side - Logo and branding -->
  <g transform="translate(150, 337.5)">
    <circle r="80" fill="#8B5CF6"/>
    <rect x="25" y="-50" width="10" height="70" fill="white" rx="5"/>
    <ellipse cx="30" cy="20" rx="20" ry="15" fill="white"/>
  </g>
  
  <!-- Right side - Text content -->
  <text x="350" y="250" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">Vibify</text>
  <text x="350" y="300" font-family="Arial, sans-serif" font-size="24" fill="#A855F7">The most beautiful music streaming app</text>
  <text x="350" y="350" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">✨ Stunning glass UI design</text>
  <text x="350" y="380" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">🎛️ Professional 10-band equalizer</text>
  <text x="350" y="410" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">📱 Available on all platforms</text>
  
  <!-- Bottom banner -->
  <rect x="0" y="575" width="1200" height="100" fill="#8B5CF6" opacity="0.2"/>
  <text x="600" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white">Download Vibify Today • vibify.app</text>
</svg>
