<svg width="600" height="200" viewBox="0 0 600 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="emailBg" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="200" fill="url(#emailBg)"/>
  
  <!-- Logo -->
  <circle cx="100" cy="100" r="40" fill="white" opacity="0.2"/>
  <circle cx="100" cy="100" r="30" fill="white"/>
  <rect x="115" y="85" width="4" height="30" fill="#8B5CF6" rx="2"/>
  <ellipse cx="117" cy="115" rx="8" ry="6" fill="#8B5CF6"/>
  
  <!-- Text -->
  <text x="160" y="90" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">Vibify</text>
  <text x="160" y="115" font-family="Arial, sans-serif" font-size="16" fill="white" opacity="0.9">Beautiful music streaming</text>
  <text x="160" y="135" font-family="Arial, sans-serif" font-size="14" fill="white" opacity="0.8">Now available on all platforms</text>
  
  <!-- CTA -->
  <rect x="450" y="75" width="120" height="50" rx="25" fill="white"/>
  <text x="510" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#8B5CF6">Get App</text>
</svg>
