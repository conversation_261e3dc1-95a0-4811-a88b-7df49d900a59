<svg width="1024" height="500" viewBox="0 0 1024 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1024" height="500" fill="url(#bgGradient)"/>
  
  <!-- Glass elements -->
  <rect x="50" y="100" width="300" height="300" rx="20" fill="url(#glassGradient)" opacity="0.3" filter="url(#blur)"/>
  <rect x="400" y="150" width="250" height="200" rx="15" fill="url(#glassGradient)" opacity="0.2" filter="url(#blur)"/>
  
  <!-- Main logo -->
  <g transform="translate(150, 200)">
    <circle r="60" fill="url(#glassGradient)"/>
    <rect x="20" y="-40" width="8" height="60" fill="white" rx="4"/>
    <ellipse cx="24" cy="20" rx="16" ry="12" fill="white"/>
  </g>
  
  <!-- Text -->
  <text x="300" y="200" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">Vibify</text>
  <text x="300" y="240" font-family="Arial, sans-serif" font-size="24" fill="#A855F7">Your Music, Your Vibe</text>
  <text x="300" y="280" font-family="Arial, sans-serif" font-size="18" fill="white" opacity="0.8">Beautiful Glass UI • Professional Equalizer • Offline Downloads</text>
  
  <!-- App Store badges area -->
  <rect x="700" y="350" width="280" height="120" rx="10" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
  <text x="720" y="380" font-family="Arial, sans-serif" font-size="16" fill="white">Available on</text>
  <text x="720" y="405" font-family="Arial, sans-serif" font-size="14" fill="#A855F7">iOS • Android • macOS • Windows • Web</text>
</svg>
