import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';

/// Script to generate app icons for Vibify
/// This creates SVG-based icons that can be converted to various formats
void main() {
  print('🎵 Generating Vibify App Icons...');
  
  // Create icons directory
  final iconsDir = Directory('assets/icons');
  if (!iconsDir.existsSync()) {
    iconsDir.createSync(recursive: true);
  }
  
  // Generate main app icon
  generateMainIcon();
  
  // Generate notification icon
  generateNotificationIcon();
  
  // Generate splash icon
  generateSplashIcon();
  
  print('✅ Icons generated successfully!');
  print('📁 Check the assets/icons/ directory');
  print('🔧 Use flutter_launcher_icons package to apply them');
}

/// Generate the main app icon with glass effect
void generateMainIcon() {
  final svg = '''
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Glass gradient -->
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A855F7;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glass highlight -->
    <linearGradient id="highlight" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0.1" />
    </linearGradient>
    
    <!-- Shadow -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Glass blur -->
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2"/>
    </filter>
  </defs>
  
  <!-- Background circle with glass effect -->
  <circle cx="512" cy="512" r="480" fill="url(#glassGradient)" filter="url(#shadow)"/>
  
  <!-- Glass highlight overlay -->
  <circle cx="512" cy="512" r="480" fill="url(#highlight)"/>
  
  <!-- Music note icon -->
  <g transform="translate(512, 512)">
    <!-- Main note stem -->
    <rect x="80" y="-200" width="24" height="280" fill="#FFFFFF" rx="12"/>
    
    <!-- Note head -->
    <ellipse cx="92" cy="80" rx="40" ry="28" fill="#FFFFFF"/>
    
    <!-- Musical staff lines (decorative) -->
    <line x1="-150" y1="-80" x2="150" y2="-80" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="-40" x2="150" y2="-40" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="0" x2="150" y2="0" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="40" x2="150" y2="40" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    <line x1="-150" y1="80" x2="150" y2="80" stroke="#FFFFFF" stroke-width="8" opacity="0.6"/>
    
    <!-- Treble clef (stylized) -->
    <path d="M -120 -60 Q -100 -100 -80 -60 Q -60 -20 -80 20 Q -100 60 -120 20 Q -140 -20 -120 -60 Z" 
          fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Sound waves -->
    <path d="M 140 -40 Q 180 -20 140 0 Q 180 20 140 40" 
          stroke="#FFFFFF" stroke-width="12" fill="none" opacity="0.7" stroke-linecap="round"/>
    <path d="M 160 -60 Q 220 -30 160 0 Q 220 30 160 60" 
          stroke="#FFFFFF" stroke-width="12" fill="none" opacity="0.5" stroke-linecap="round"/>
  </g>
  
  <!-- Glass reflection -->
  <ellipse cx="400" cy="300" rx="120" ry="200" fill="#FFFFFF" opacity="0.15" transform="rotate(-30 400 300)"/>
</svg>
''';

  File('assets/icons/app_icon.svg').writeAsStringSync(svg);
  print('📱 Generated main app icon (app_icon.svg)');
}

/// Generate notification icon (simpler, monochrome)
void generateNotificationIcon() {
  final svg = '''
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="notifGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="128" cy="128" r="120" fill="url(#notifGradient)"/>
  
  <!-- Music note -->
  <g transform="translate(128, 128)">
    <rect x="20" y="-50" width="8" height="70" fill="#FFFFFF" rx="4"/>
    <ellipse cx="24" cy="20" rx="12" ry="8" fill="#FFFFFF"/>
    
    <!-- Sound wave -->
    <path d="M 40 -10 Q 60 0 40 10" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-linecap="round"/>
  </g>
</svg>
''';

  File('assets/icons/notification_icon.svg').writeAsStringSync(svg);
  print('🔔 Generated notification icon (notification_icon.svg)');
}

/// Generate splash screen icon (larger, more detailed)
void generateSplashIcon() {
  final svg = '''
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="splashGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#A855F7;stop-opacity:0.9" />
      <stop offset="70%" style="stop-color:#7C3AED;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#6D28D9;stop-opacity:1" />
    </linearGradient>
    
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4"/>
      <feColorMatrix values="1 0 1 0 0  0 1 1 0 0  1 0 1 0 0  0 0 0 1 0"/>
    </filter>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="32" y="32" width="448" height="448" rx="80" fill="url(#splashGradient)"/>
  
  <!-- Glow effect -->
  <rect x="32" y="32" width="448" height="448" rx="80" fill="url(#splashGradient)" filter="url(#glow)" opacity="0.5"/>
  
  <!-- Music note -->
  <g transform="translate(256, 256)">
    <!-- Note stem -->
    <rect x="40" y="-100" width="16" height="140" fill="#FFFFFF" rx="8"/>
    
    <!-- Note head -->
    <ellipse cx="48" cy="40" rx="24" ry="16" fill="#FFFFFF"/>
    
    <!-- Decorative elements -->
    <circle cx="-60" cy="-40" r="8" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="-80" cy="0" r="6" fill="#FFFFFF" opacity="0.4"/>
    <circle cx="-70" cy="40" r="4" fill="#FFFFFF" opacity="0.3"/>
    
    <circle cx="100" cy="-60" r="6" fill="#FFFFFF" opacity="0.5"/>
    <circle cx="120" cy="-20" r="8" fill="#FFFFFF" opacity="0.4"/>
    <circle cx="110" cy="20" r="4" fill="#FFFFFF" opacity="0.3"/>
  </g>
</svg>
''';

  File('assets/icons/splash_icon.svg').writeAsStringSync(svg);
  print('🚀 Generated splash icon (splash_icon.svg)');
}
