import 'dart:io';
import 'dart:math' as math;

/// Script to generate marketing assets and promotional materials for Vibify
void main() {
  print('🎨 Generating Vibify Marketing Assets...');
  
  // Create marketing directory
  final marketingDir = Directory('marketing/assets');
  if (!marketingDir.existsSync()) {
    marketingDir.createSync(recursive: true);
  }
  
  // Generate various marketing assets
  generateFeatureGraphics();
  generateSocialMediaAssets();
  generateAppStoreGraphics();
  generatePromotionalBanners();
  
  print('✅ Marketing assets generated successfully!');
  print('📁 Check the marketing/assets/ directory');
}

/// Generate feature graphics for app stores
void generateFeatureGraphics() {
  print('📱 Generating feature graphics...');
  
  // Google Play Feature Graphic (1024x500)
  final playFeatureGraphic = '''
<svg width="1024" height="500" viewBox="0 0 1024 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1024" height="500" fill="url(#bgGradient)"/>
  
  <!-- Glass elements -->
  <rect x="50" y="100" width="300" height="300" rx="20" fill="url(#glassGradient)" opacity="0.3" filter="url(#blur)"/>
  <rect x="400" y="150" width="250" height="200" rx="15" fill="url(#glassGradient)" opacity="0.2" filter="url(#blur)"/>
  
  <!-- Main logo -->
  <g transform="translate(150, 200)">
    <circle r="60" fill="url(#glassGradient)"/>
    <rect x="20" y="-40" width="8" height="60" fill="white" rx="4"/>
    <ellipse cx="24" cy="20" rx="16" ry="12" fill="white"/>
  </g>
  
  <!-- Text -->
  <text x="300" y="200" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">Vibify</text>
  <text x="300" y="240" font-family="Arial, sans-serif" font-size="24" fill="#A855F7">Your Music, Your Vibe</text>
  <text x="300" y="280" font-family="Arial, sans-serif" font-size="18" fill="white" opacity="0.8">Beautiful Glass UI • Professional Equalizer • Offline Downloads</text>
  
  <!-- App Store badges area -->
  <rect x="700" y="350" width="280" height="120" rx="10" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
  <text x="720" y="380" font-family="Arial, sans-serif" font-size="16" fill="white">Available on</text>
  <text x="720" y="405" font-family="Arial, sans-serif" font-size="14" fill="#A855F7">iOS • Android • macOS • Windows • Web</text>
</svg>
''';

  File('marketing/assets/play_store_feature_graphic.svg').writeAsStringSync(playFeatureGraphic);
  print('📱 Generated Google Play feature graphic');
}

/// Generate social media assets
void generateSocialMediaAssets() {
  print('📱 Generating social media assets...');
  
  // Instagram Story Template (1080x1920)
  final instagramStory = '''
<svg width="1080" height="1920" viewBox="0 0 1080 1920" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="storyBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A855F7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1080" height="1920" fill="url(#storyBg)"/>
  
  <!-- Decorative elements -->
  <circle cx="200" cy="300" r="100" fill="white" opacity="0.1" filter="url(#glow)"/>
  <circle cx="880" cy="600" r="80" fill="white" opacity="0.15" filter="url(#glow)"/>
  <circle cx="150" cy="1200" r="60" fill="white" opacity="0.1" filter="url(#glow)"/>
  
  <!-- Main content -->
  <g transform="translate(540, 800)">
    <!-- Logo -->
    <circle r="120" fill="white" opacity="0.2"/>
    <circle r="100" fill="white"/>
    <rect x="30" y="-60" width="12" height="90" fill="#8B5CF6" rx="6"/>
    <ellipse cx="36" cy="30" rx="24" ry="18" fill="#8B5CF6"/>
  </g>
  
  <!-- Text content -->
  <text x="540" y="1050" text-anchor="middle" font-family="Arial, sans-serif" font-size="64" font-weight="bold" fill="white">Vibify</text>
  <text x="540" y="1120" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" fill="white" opacity="0.9">Your Music, Your Vibe</text>
  
  <text x="540" y="1250" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" fill="white">🎵 Beautiful Glass UI</text>
  <text x="540" y="1300" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" fill="white">🎛️ Professional Equalizer</text>
  <text x="540" y="1350" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" fill="white">📱 Available Everywhere</text>
  
  <!-- Call to action -->
  <rect x="240" y="1500" width="600" height="80" rx="40" fill="white"/>
  <text x="540" y="1555" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#8B5CF6">Download Now</text>
</svg>
''';

  File('marketing/assets/instagram_story_template.svg').writeAsStringSync(instagramStory);
  print('📱 Generated Instagram story template');
  
  // Twitter Card (1200x675)
  final twitterCard = '''
<svg width="1200" height="675" viewBox="0 0 1200 675" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="twitterBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="675" fill="url(#twitterBg)"/>
  
  <!-- Left side - Logo and branding -->
  <g transform="translate(150, 337.5)">
    <circle r="80" fill="#8B5CF6"/>
    <rect x="25" y="-50" width="10" height="70" fill="white" rx="5"/>
    <ellipse cx="30" cy="20" rx="20" ry="15" fill="white"/>
  </g>
  
  <!-- Right side - Text content -->
  <text x="350" y="250" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">Vibify</text>
  <text x="350" y="300" font-family="Arial, sans-serif" font-size="24" fill="#A855F7">The most beautiful music streaming app</text>
  <text x="350" y="350" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">✨ Stunning glass UI design</text>
  <text x="350" y="380" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">🎛️ Professional 10-band equalizer</text>
  <text x="350" y="410" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">📱 Available on all platforms</text>
  
  <!-- Bottom banner -->
  <rect x="0" y="575" width="1200" height="100" fill="#8B5CF6" opacity="0.2"/>
  <text x="600" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="white">Download Vibify Today • vibify.app</text>
</svg>
''';

  File('marketing/assets/twitter_card.svg').writeAsStringSync(twitterCard);
  print('📱 Generated Twitter card');
}

/// Generate app store specific graphics
void generateAppStoreGraphics() {
  print('📱 Generating app store graphics...');
  
  // App Store promotional graphic
  final appStorePromo = '''
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="promoBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f0f23;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a2e;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="spotlight" cx="50%" cy="30%" r="60%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="url(#promoBg)"/>
  <rect width="1200" height="800" fill="url(#spotlight)"/>
  
  <!-- Phone mockup outline -->
  <rect x="100" y="100" width="300" height="600" rx="30" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
  
  <!-- App interface mockup -->
  <rect x="120" y="150" width="260" height="500" rx="20" fill="#8B5CF6" opacity="0.1"/>
  <circle cx="250" cy="300" r="60" fill="#8B5CF6"/>
  <rect x="230" y="270" width="6" height="60" fill="white" rx="3"/>
  <ellipse cx="233" cy="330" rx="12" ry="9" fill="white"/>
  
  <!-- Main content -->
  <text x="500" y="200" font-family="Arial, sans-serif" font-size="64" font-weight="bold" fill="white">Vibify</text>
  <text x="500" y="250" font-family="Arial, sans-serif" font-size="32" fill="#A855F7">Your Music, Your Vibe</text>
  
  <text x="500" y="320" font-family="Arial, sans-serif" font-size="24" fill="white">🎨 Revolutionary Glass UI Design</text>
  <text x="500" y="360" font-family="Arial, sans-serif" font-size="24" fill="white">🎛️ Professional 10-Band Equalizer</text>
  <text x="500" y="400" font-family="Arial, sans-serif" font-size="24" fill="white">📥 Smart Offline Downloads</text>
  <text x="500" y="440" font-family="Arial, sans-serif" font-size="24" fill="white">🔍 Enhanced Music Discovery</text>
  <text x="500" y="480" font-family="Arial, sans-serif" font-size="24" fill="white">⚙️ 40+ Customization Options</text>
  
  <!-- Awards section -->
  <rect x="500" y="550" width="600" height="150" rx="15" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)"/>
  <text x="520" y="580" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#A855F7">Perfect for:</text>
  <text x="520" y="610" font-family="Arial, sans-serif" font-size="18" fill="white">• Music enthusiasts who love beautiful design</text>
  <text x="520" y="635" font-family="Arial, sans-serif" font-size="18" fill="white">• Audiophiles seeking professional controls</text>
  <text x="520" y="660" font-family="Arial, sans-serif" font-size="18" fill="white">• Anyone who appreciates exceptional user experience</text>
</svg>
''';

  File('marketing/assets/app_store_promo.svg').writeAsStringSync(appStorePromo);
  print('📱 Generated App Store promotional graphic');
}

/// Generate promotional banners
void generatePromotionalBanners() {
  print('📱 Generating promotional banners...');
  
  // Website hero banner
  final heroBanner = '''
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f0f23;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="heroSpotlight" cx="30%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="url(#heroBg)"/>
  <rect width="1920" height="1080" fill="url(#heroSpotlight)"/>
  
  <!-- Decorative elements -->
  <circle cx="300" cy="200" r="150" fill="white" opacity="0.03"/>
  <circle cx="1600" cy="300" r="200" fill="white" opacity="0.02"/>
  <circle cx="200" cy="800" r="100" fill="white" opacity="0.04"/>
  
  <!-- Main content -->
  <g transform="translate(960, 400)">
    <!-- Logo -->
    <circle r="100" fill="#8B5CF6" opacity="0.8"/>
    <rect x="30" y="-60" width="12" height="90" fill="white" rx="6"/>
    <ellipse cx="36" cy="30" rx="24" ry="18" fill="white"/>
  </g>
  
  <!-- Text content -->
  <text x="960" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="80" font-weight="bold" fill="white">Vibify</text>
  <text x="960" y="640" text-anchor="middle" font-family="Arial, sans-serif" font-size="40" fill="#A855F7">Your Music, Your Vibe</text>
  <text x="960" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" fill="white" opacity="0.9">The most beautiful music streaming experience ever created</text>
  
  <!-- Feature highlights -->
  <g transform="translate(960, 800)">
    <text x="-300" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">🎨 Glass UI</text>
    <text x="-100" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">🎛️ Pro EQ</text>
    <text x="100" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">📱 All Platforms</text>
    <text x="300" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="white">🎵 Offline Music</text>
  </g>
  
  <!-- Call to action -->
  <rect x="760" y="900" width="400" height="80" rx="40" fill="#8B5CF6"/>
  <text x="960" y="955" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">Download Free</text>
</svg>
''';

  File('marketing/assets/hero_banner.svg').writeAsStringSync(heroBanner);
  print('📱 Generated hero banner');
  
  // Email newsletter banner
  final emailBanner = '''
<svg width="600" height="200" viewBox="0 0 600 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="emailBg" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="200" fill="url(#emailBg)"/>
  
  <!-- Logo -->
  <circle cx="100" cy="100" r="40" fill="white" opacity="0.2"/>
  <circle cx="100" cy="100" r="30" fill="white"/>
  <rect x="115" y="85" width="4" height="30" fill="#8B5CF6" rx="2"/>
  <ellipse cx="117" cy="115" rx="8" ry="6" fill="#8B5CF6"/>
  
  <!-- Text -->
  <text x="160" y="90" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">Vibify</text>
  <text x="160" y="115" font-family="Arial, sans-serif" font-size="16" fill="white" opacity="0.9">Beautiful music streaming</text>
  <text x="160" y="135" font-family="Arial, sans-serif" font-size="14" fill="white" opacity="0.8">Now available on all platforms</text>
  
  <!-- CTA -->
  <rect x="450" y="75" width="120" height="50" rx="25" fill="white"/>
  <text x="510" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#8B5CF6">Get App</text>
</svg>
''';

  File('marketing/assets/email_banner.svg').writeAsStringSync(emailBanner);
  print('📱 Generated email banner');
}
