# 🎵 Vibify - Your Music, Your Vibe

<div align="center">

**A beautiful music streaming app with stunning glass UI design**

[![Flutter](https://img.shields.io/badge/Flutter-3.0+-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.0+-blue.svg)](https://dart.dev/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

</div>

## ✨ Features

### 🎵 **Core Music Features**

- **🎧 High-Quality Audio Playback** - Background audio with full controls
- **📥 Download & Offline Mode** - Save music for offline listening
- **📚 Smart Library Management** - Organize with playlists and favorites
- **🔍 YouTube Integration** - Search and discover music from YouTube
- **⚙️ Comprehensive Settings** - 40+ customization options

### 🎨 **Enhanced User Experience**

- **🌟 Glass UI Design** - Stunning translucent interface throughout
- **🎵 Full-Screen Player** - Enhanced player with audio visualizations
- **🚀 Beautiful Splash Screen** - Animated app initialization
- **📱 Responsive Design** - Perfect on all screen sizes
- **✨ Smooth Animations** - 60fps performance with beautiful transitions

### ⚡ **Performance & Reliability**

- **🔧 Reliable Storage** - Advanced database management with error recovery
- **🎯 Memory Optimized** - Efficient resource usage and cleanup
- **🔄 Error Recovery** - Graceful handling of all edge cases
- **📊 Loading States** - Beautiful loading indicators throughout

## 🚀 Getting Started

### Prerequisites

- Flutter 3.0 or higher
- Dart 3.0 or higher
- macOS 10.14+ (for macOS builds)

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/vibify.git
   cd vibify
   ```

2. **Install dependencies**

   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

## 🏗️ Architecture

Vibify follows a clean architecture pattern with clear separation of concerns:

```
lib/
├── controllers/          # State management (Provider pattern)
├── services/            # Business logic and external APIs
├── models/              # Data models with Hive persistence
├── ui/
│   ├── screens/         # App screens and pages
│   ├── widgets/         # Reusable UI components
│   └── theme/           # Design system and theming
└── main.dart           # App entry point
```

### 🔧 **Key Technologies**

- **Flutter & Dart** - Cross-platform UI framework
- **Provider** - State management solution
- **Hive** - Fast, lightweight database
- **Just Audio** - High-quality audio playback
- **HTTP** - Network requests and API integration

## 🎨 Design System

Vibify features a comprehensive glass-themed design system with translucent containers, blur effects, and dynamic gradients throughout the interface.

## 📄 License

This project is licensed under the MIT License.

---

<div align="center">
  <p><strong>Made with ❤️ and Flutter</strong></p>
  <p>Vibify - Your Music, Your Vibe 🎵</p>
</div>
