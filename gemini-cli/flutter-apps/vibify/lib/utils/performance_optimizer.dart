import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';

/// Performance optimization utilities for Vibify
class PerformanceOptimizer {
  static final PerformanceOptimizer _instance =
      PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  // Performance monitoring
  final List<double> _frameTimes = [];
  Timer? _performanceTimer;

  double _averageFPS = 60.0;

  /// Initialize performance monitoring
  void initialize() {
    if (kDebugMode) {
      _startPerformanceMonitoring();
    }
    _optimizeSystemSettings();
  }

  /// Start monitoring frame performance
  void _startPerformanceMonitoring() {
    _performanceTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_frameTimes.isNotEmpty) {
        _averageFPS =
            1000 / (_frameTimes.reduce((a, b) => a + b) / _frameTimes.length);
        _frameTimes.clear();

        if (kDebugMode) {
          debugPrint(
            'Vibify Performance: ${_averageFPS.toStringAsFixed(1)} FPS',
          );
        }
      }
    });
  }

  /// Record frame time for performance monitoring
  void recordFrameTime(double frameTime) {
    if (_frameTimes.length > 60) {
      _frameTimes.removeAt(0);
    }
    _frameTimes.add(frameTime);
  }

  /// Get current average FPS
  double get averageFPS => _averageFPS;

  /// Optimize system settings for better performance
  void _optimizeSystemSettings() {
    // Enable hardware acceleration
    if (Platform.isAndroid || Platform.isIOS) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }

    // Optimize for 60fps
    if (kDebugMode) {
      debugPrint('Performance optimization enabled');
    }
  }

  /// Dispose performance monitoring
  void dispose() {
    _performanceTimer?.cancel();
  }
}

/// Memory management utilities
class MemoryManager {
  static final Map<String, dynamic> _cache = {};
  static const int _maxCacheSize = 100;

  /// Cache data with automatic cleanup
  static void cache(String key, dynamic data) {
    if (_cache.length >= _maxCacheSize) {
      // Remove oldest entries
      final keys = _cache.keys.toList();
      for (int i = 0; i < 10; i++) {
        _cache.remove(keys[i]);
      }
    }
    _cache[key] = data;
  }

  /// Get cached data
  static T? getCached<T>(String key) {
    return _cache[key] as T?;
  }

  /// Clear specific cache entry
  static void clearCache(String key) {
    _cache.remove(key);
  }

  /// Clear all cache
  static void clearAllCache() {
    _cache.clear();
    if (kDebugMode) {
      debugPrint('Memory cache cleared');
    }
  }

  /// Get cache size
  static int get cacheSize => _cache.length;
}

/// Image optimization utilities
class ImageOptimizer {
  /// Optimize image loading with caching
  static ImageProvider optimizeImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    // Use cached network image for better performance
    return NetworkImage(imageUrl);
  }

  /// Preload critical images
  static Future<void> preloadImages(
    BuildContext context,
    List<String> imageUrls,
  ) async {
    final futures = imageUrls.map((url) {
      return precacheImage(NetworkImage(url), context);
    });

    await Future.wait(futures);
    if (kDebugMode) {
      debugPrint('Preloaded ${imageUrls.length} images');
    }
  }

  /// Optimize image memory usage
  static Widget optimizedImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) return child;
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          child: child,
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              width: width,
              height: height,
              color: Colors.grey.withValues(alpha: 0.3),
              child: const Icon(Icons.error, color: Colors.grey),
            );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Container(
              width: width,
              height: height,
              color: Colors.grey.withValues(alpha: 0.1),
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            );
      },
    );
  }
}

/// List performance optimization
class ListOptimizer {
  /// Create optimized list view for large datasets
  static Widget optimizedListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Add automatic disposal for off-screen items
        return _OptimizedListItem(
          key: ValueKey(index),
          child: itemBuilder(context, index),
        );
      },
      // Optimize for performance
      cacheExtent: 500, // Cache 500 pixels ahead
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: true,
    );
  }

  /// Create optimized grid view
  static Widget optimizedGridView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    EdgeInsets? padding,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      itemCount: itemCount,
      gridDelegate: gridDelegate,
      itemBuilder: (context, index) {
        return _OptimizedListItem(
          key: ValueKey(index),
          child: itemBuilder(context, index),
        );
      },
      cacheExtent: 500,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: true,
    );
  }
}

/// Optimized list item wrapper
class _OptimizedListItem extends StatefulWidget {
  final Widget child;

  const _OptimizedListItem({super.key, required this.child});

  @override
  State<_OptimizedListItem> createState() => _OptimizedListItemState();
}

class _OptimizedListItemState extends State<_OptimizedListItem>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => false; // Don't keep alive to save memory

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return RepaintBoundary(child: widget.child);
  }
}

/// Animation performance optimizer
class AnimationOptimizer {
  /// Create performance-optimized animation controller
  static AnimationController createOptimizedController({
    required Duration duration,
    required TickerProvider vsync,
    double? value,
    Duration? reverseDuration,
  }) {
    return AnimationController(
      duration: duration,
      reverseDuration: reverseDuration,
      vsync: vsync,
      value: value,
    );
  }

  /// Optimize animation for 60fps
  static Animation<double> optimizeAnimation(
    Animation<double> animation, {
    Curve curve = Curves.easeInOut,
  }) {
    return CurvedAnimation(parent: animation, curve: curve);
  }

  /// Create optimized fade transition
  static Widget optimizedFadeTransition({
    required Animation<double> opacity,
    required Widget child,
  }) {
    return AnimatedBuilder(
      animation: opacity,
      builder: (context, child) {
        return Opacity(opacity: opacity.value, child: child);
      },
      child: child,
    );
  }
}

/// Network optimization utilities
class NetworkOptimizer {
  static final Map<String, dynamic> _networkCache = {};
  static const Duration _cacheTimeout = Duration(minutes: 5);

  /// Optimized network request with caching
  static Future<T?> cachedRequest<T>(
    String key,
    Future<T> Function() request, {
    Duration? timeout,
  }) async {
    // Check cache first
    final cached = _networkCache[key];
    if (cached != null && cached['timestamp'] != null) {
      final age = DateTime.now().difference(cached['timestamp'] as DateTime);
      if (age < _cacheTimeout) {
        return cached['data'] as T;
      }
    }

    try {
      final result = await request().timeout(
        timeout ?? const Duration(seconds: 10),
      );

      // Cache the result
      _networkCache[key] = {'data': result, 'timestamp': DateTime.now()};

      return result;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Network request failed: $e');
      }
      return null;
    }
  }

  /// Clear network cache
  static void clearNetworkCache() {
    _networkCache.clear();
  }
}

/// Lazy loading utilities
class LazyLoader {
  /// Create lazy loading widget
  static Widget lazyWidget({
    required Widget Function() builder,
    Widget? placeholder,
    bool preload = false,
  }) {
    return _LazyWidget(
      builder: builder,
      placeholder: placeholder,
      preload: preload,
    );
  }

  /// Lazy load list items
  static Widget lazyListItem({
    required Widget Function() builder,
    required bool isVisible,
    Widget? placeholder,
  }) {
    if (!isVisible) {
      return placeholder ?? const SizedBox.shrink();
    }
    return builder();
  }
}

/// Lazy widget implementation
class _LazyWidget extends StatefulWidget {
  final Widget Function() builder;
  final Widget? placeholder;
  final bool preload;

  const _LazyWidget({
    required this.builder,
    this.placeholder,
    this.preload = false,
  });

  @override
  State<_LazyWidget> createState() => _LazyWidgetState();
}

class _LazyWidgetState extends State<_LazyWidget> {
  Widget? _builtWidget;
  bool _isBuilt = false;

  @override
  void initState() {
    super.initState();
    if (widget.preload) {
      _buildWidget();
    }
  }

  void _buildWidget() {
    if (!_isBuilt) {
      _builtWidget = widget.builder();
      _isBuilt = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isBuilt) {
      // Build on first render
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _buildWidget();
          });
        }
      });

      return widget.placeholder ?? const SizedBox.shrink();
    }

    return _builtWidget!;
  }
}

/// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool showOverlay;

  const PerformanceMonitor({
    super.key,
    required this.child,
    this.showOverlay = false,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  final PerformanceOptimizer _optimizer = PerformanceOptimizer();

  @override
  void initState() {
    super.initState();
    _optimizer.initialize();
  }

  @override
  void dispose() {
    _optimizer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.showOverlay && kDebugMode)
          Positioned(
            top: 50,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'FPS: ${_optimizer.averageFPS.toStringAsFixed(1)}\n'
                'Cache: ${MemoryManager.cacheSize}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ),
      ],
    );
  }
}
