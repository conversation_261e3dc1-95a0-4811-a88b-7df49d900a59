import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Audio quality options
enum AudioQuality { low, medium, high, lossless }

/// Download quality options  
enum DownloadQuality { low, medium, high, lossless }

/// Theme mode options
enum AppThemeMode { system, light, dark, glassDark }

/// Service for managing app settings and preferences
class SettingsService {
  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  SettingsService._();

  // Settings storage (in-memory for now, can be persisted later)
  final Map<String, dynamic> _settings = {};

  // Stream controllers for settings changes
  final StreamController<Map<String, dynamic>> _settingsController = StreamController.broadcast();

  // Getters for streams
  Stream<Map<String, dynamic>> get settingsStream => _settingsController.stream;

  // Default settings
  static const Map<String, dynamic> _defaultSettings = {
    // Audio settings
    'audio_quality': AudioQuality.high,
    'volume': 0.8,
    'crossfade_duration': 3.0,
    'normalize_volume': true,
    'gapless_playback': true,
    'replay_gain': false,
    
    // Download settings
    'download_quality': DownloadQuality.high,
    'download_over_wifi_only': true,
    'auto_download_favorites': false,
    'max_concurrent_downloads': 3,
    'download_location': 'default',
    
    // Appearance settings
    'theme_mode': AppThemeMode.system,
    'glass_intensity': 0.8,
    'show_album_art': true,
    'show_lyrics': true,
    'animated_backgrounds': true,
    'reduce_motion': false,
    
    // Playback settings
    'auto_play_next': true,
    'shuffle_mode': false,
    'repeat_mode': 'off', // 'off', 'all', 'one'
    'skip_silence': false,
    'remember_playback_position': true,
    
    // Library settings
    'auto_add_to_library': true,
    'show_explicit_content': true,
    'group_by_album_artist': false,
    'sort_albums_by_year': true,
    'show_recently_added': true,
    
    // Search settings
    'search_suggestions': true,
    'save_search_history': true,
    'trending_music_region': 'global',
    'explicit_search_results': true,
    
    // Privacy settings
    'analytics_enabled': true,
    'crash_reporting': true,
    'personalized_recommendations': true,
    'share_listening_activity': false,
    
    // Notifications settings
    'show_notifications': true,
    'download_notifications': true,
    'playback_notifications': true,
    'new_music_notifications': false,
    
    // Advanced settings
    'developer_mode': false,
    'debug_logging': false,
    'cache_size_mb': 500,
    'preload_next_track': true,
  };

  /// Initialize the settings service
  Future<void> initialize() async {
    try {
      // Load default settings
      _settings.addAll(_defaultSettings);
      
      // TODO: Load persisted settings from storage
      await _loadPersistedSettings();
      
      debugPrint('SettingsService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize SettingsService: $e');
      rethrow;
    }
  }

  /// Get a setting value
  T getSetting<T>(String key, {T? defaultValue}) {
    final value = _settings[key];
    if (value is T) {
      return value;
    }
    return defaultValue ?? _defaultSettings[key] as T;
  }

  /// Set a setting value
  Future<void> setSetting<T>(String key, T value) async {
    try {
      _settings[key] = value;
      await _persistSetting(key, value);
      _settingsController.add(Map.from(_settings));
      debugPrint('Setting updated: $key = $value');
    } catch (e) {
      debugPrint('Failed to set setting $key: $e');
      rethrow;
    }
  }

  /// Get multiple settings
  Map<String, dynamic> getSettings(List<String> keys) {
    final result = <String, dynamic>{};
    for (final key in keys) {
      result[key] = _settings[key];
    }
    return result;
  }

  /// Set multiple settings
  Future<void> setSettings(Map<String, dynamic> settings) async {
    try {
      _settings.addAll(settings);
      await _persistSettings(settings);
      _settingsController.add(Map.from(_settings));
      debugPrint('Multiple settings updated: ${settings.keys}');
    } catch (e) {
      debugPrint('Failed to set multiple settings: $e');
      rethrow;
    }
  }

  /// Reset settings to defaults
  Future<void> resetToDefaults() async {
    try {
      _settings.clear();
      _settings.addAll(_defaultSettings);
      await _persistAllSettings();
      _settingsController.add(Map.from(_settings));
      debugPrint('Settings reset to defaults');
    } catch (e) {
      debugPrint('Failed to reset settings: $e');
      rethrow;
    }
  }

  /// Reset specific category to defaults
  Future<void> resetCategoryToDefaults(String category) async {
    try {
      final categoryKeys = _getCategoryKeys(category);
      for (final key in categoryKeys) {
        if (_defaultSettings.containsKey(key)) {
          _settings[key] = _defaultSettings[key];
        }
      }
      await _persistSettings(Map.fromEntries(
        categoryKeys.map((key) => MapEntry(key, _settings[key]))
      ));
      _settingsController.add(Map.from(_settings));
      debugPrint('Category $category reset to defaults');
    } catch (e) {
      debugPrint('Failed to reset category $category: $e');
      rethrow;
    }
  }

  /// Get all settings
  Map<String, dynamic> getAllSettings() {
    return Map.unmodifiable(_settings);
  }

  /// Check if setting exists
  bool hasSetting(String key) {
    return _settings.containsKey(key);
  }

  /// Get setting keys for a category
  List<String> _getCategoryKeys(String category) {
    switch (category) {
      case 'audio':
        return ['audio_quality', 'volume', 'crossfade_duration', 'normalize_volume', 'gapless_playback', 'replay_gain'];
      case 'download':
        return ['download_quality', 'download_over_wifi_only', 'auto_download_favorites', 'max_concurrent_downloads', 'download_location'];
      case 'appearance':
        return ['theme_mode', 'glass_intensity', 'show_album_art', 'show_lyrics', 'animated_backgrounds', 'reduce_motion'];
      case 'playback':
        return ['auto_play_next', 'shuffle_mode', 'repeat_mode', 'skip_silence', 'remember_playback_position'];
      case 'library':
        return ['auto_add_to_library', 'show_explicit_content', 'group_by_album_artist', 'sort_albums_by_year', 'show_recently_added'];
      case 'search':
        return ['search_suggestions', 'save_search_history', 'trending_music_region', 'explicit_search_results'];
      case 'privacy':
        return ['analytics_enabled', 'crash_reporting', 'personalized_recommendations', 'share_listening_activity'];
      case 'notifications':
        return ['show_notifications', 'download_notifications', 'playback_notifications', 'new_music_notifications'];
      case 'advanced':
        return ['developer_mode', 'debug_logging', 'cache_size_mb', 'preload_next_track'];
      default:
        return [];
    }
  }

  /// Load persisted settings from storage
  Future<void> _loadPersistedSettings() async {
    try {
      // TODO: Implement actual persistence with Hive or SharedPreferences
      // For now, we'll use in-memory storage
      debugPrint('Settings loaded from storage (in-memory only)');
    } catch (e) {
      debugPrint('Failed to load persisted settings: $e');
    }
  }

  /// Persist a single setting
  Future<void> _persistSetting(String key, dynamic value) async {
    try {
      // TODO: Implement actual persistence
      debugPrint('Setting persisted: $key = $value (in-memory only)');
    } catch (e) {
      debugPrint('Failed to persist setting $key: $e');
    }
  }

  /// Persist multiple settings
  Future<void> _persistSettings(Map<String, dynamic> settings) async {
    try {
      // TODO: Implement actual persistence
      debugPrint('Settings persisted: ${settings.keys} (in-memory only)');
    } catch (e) {
      debugPrint('Failed to persist settings: $e');
    }
  }

  /// Persist all settings
  Future<void> _persistAllSettings() async {
    try {
      // TODO: Implement actual persistence
      debugPrint('All settings persisted (in-memory only)');
    } catch (e) {
      debugPrint('Failed to persist all settings: $e');
    }
  }

  /// Get formatted setting value for display
  String getFormattedValue(String key) {
    final value = _settings[key];
    
    switch (key) {
      case 'audio_quality':
      case 'download_quality':
        return (value as Enum).name.toUpperCase();
      case 'theme_mode':
        return (value as AppThemeMode).name.replaceAll('_', ' ').toUpperCase();
      case 'volume':
      case 'glass_intensity':
        return '${((value as double) * 100).round()}%';
      case 'crossfade_duration':
        return '${(value as double).toStringAsFixed(1)}s';
      case 'cache_size_mb':
        return '${value}MB';
      case 'max_concurrent_downloads':
        return '$value downloads';
      default:
        return value.toString();
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _settingsController.close();
  }
}
