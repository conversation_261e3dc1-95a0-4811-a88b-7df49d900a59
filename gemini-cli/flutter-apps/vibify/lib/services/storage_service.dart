import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/song.dart';
import '../models/playlist.dart';
import '../models/artist.dart';
import '../models/album.dart';
import '../models/download.dart';
import '../models/duration_adapter.dart';

class StorageService {
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  StorageService._();

  // Storage paths
  late String _documentsPath;
  late String _musicPath;
  late String _thumbnailsPath;
  late String _cachePath;

  // Hive boxes
  Box<Song>? _songsBox;
  Box<Playlist>? _playlistsBox;
  Box<Artist>? _artistsBox;
  Box<Album>? _albumsBox;
  Box<Download>? _downloadsBox;

  // Getters for boxes
  Box<Song> get songsBox => _songsBox!;
  Box<Playlist> get playlistsBox => _playlistsBox!;
  Box<Artist> get artistsBox => _artistsBox!;
  Box<Album> get albumsBox => _albumsBox!;
  Box<Download> get downloadsBox => _downloadsBox!;

  // Path getters
  String get documentsPath => _documentsPath;
  String get musicPath => _musicPath;
  String get thumbnailsPath => _thumbnailsPath;
  String get cachePath => _cachePath;

  // Initialize storage service
  Future<void> initialize() async {
    try {
      // Initialize Hive
      await Hive.initFlutter();

      // Register adapters
      _registerHiveAdapters();

      // Setup storage paths
      await _setupStoragePaths();

      // Open Hive boxes
      await _openHiveBoxes();

      debugPrint('StorageService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize StorageService: $e');
      rethrow;
    }
  }

  // Register Hive type adapters
  void _registerHiveAdapters() {
    // Register Duration adapter to fix storage warnings
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(DurationAdapter());
    }

    // Register generated adapters - temporarily disabled for testing
    try {
      // Note: Adapters will be registered when Hive generation is working properly
      debugPrint(
        'Hive adapters registration skipped - using in-memory storage for now',
      );
    } catch (e) {
      debugPrint('Hive adapters registration error: $e');
    }
  }

  // Setup storage directory paths
  Future<void> _setupStoragePaths() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    _documentsPath = documentsDir.path;

    // Create subdirectories
    _musicPath = '$_documentsPath/Music';
    _thumbnailsPath = '$_documentsPath/Thumbnails';
    _cachePath = '$_documentsPath/Cache';

    // Ensure directories exist
    await _ensureDirectoryExists(_musicPath);
    await _ensureDirectoryExists(_thumbnailsPath);
    await _ensureDirectoryExists(_cachePath);
  }

  // Open all Hive boxes with retry mechanism
  Future<void> _openHiveBoxes() async {
    try {
      // Close any existing boxes first to prevent lock issues
      await _closeBoxesSafely();

      // Open boxes with retry mechanism
      _songsBox = await _openBoxWithRetry<Song>('songs');
      _playlistsBox = await _openBoxWithRetry<Playlist>('playlists');
      _artistsBox = await _openBoxWithRetry<Artist>('artists');
      _albumsBox = await _openBoxWithRetry<Album>('albums');
      _downloadsBox = await _openBoxWithRetry<Download>('downloads');
    } catch (e) {
      debugPrint('Failed to open Hive boxes: $e');
      // Try to clear lock files and retry once
      await _clearLockFiles();
      await Future.delayed(const Duration(milliseconds: 500));

      _songsBox = await _openBoxWithRetry<Song>('songs');
      _playlistsBox = await _openBoxWithRetry<Playlist>('playlists');
      _artistsBox = await _openBoxWithRetry<Artist>('artists');
      _albumsBox = await _openBoxWithRetry<Album>('albums');
      _downloadsBox = await _openBoxWithRetry<Download>('downloads');
    }
  }

  // Ensure directory exists
  Future<void> _ensureDirectoryExists(String path) async {
    final directory = Directory(path);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }

  // File operations
  Future<String> saveMusicFile(String fileName, List<int> bytes) async {
    final filePath = '$_musicPath/$fileName';
    final file = File(filePath);
    await file.writeAsBytes(bytes);
    return filePath;
  }

  Future<String> saveThumbnail(String fileName, List<int> bytes) async {
    final filePath = '$_thumbnailsPath/$fileName';
    final file = File(filePath);
    await file.writeAsBytes(bytes);
    return filePath;
  }

  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Failed to delete file: $e');
      return false;
    }
  }

  Future<bool> fileExists(String filePath) async {
    final file = File(filePath);
    return await file.exists();
  }

  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      debugPrint('Failed to get file size: $e');
      return 0;
    }
  }

  // Storage info
  Future<StorageInfo> getStorageInfo() async {
    try {
      int totalMusicSize = 0;
      int totalThumbnailSize = 0;
      int totalCacheSize = 0;
      int musicFileCount = 0;
      int thumbnailFileCount = 0;

      // Calculate music directory size
      final musicDir = Directory(_musicPath);
      if (await musicDir.exists()) {
        await for (final entity in musicDir.list(recursive: true)) {
          if (entity is File) {
            totalMusicSize += await entity.length();
            musicFileCount++;
          }
        }
      }

      // Calculate thumbnails directory size
      final thumbnailsDir = Directory(_thumbnailsPath);
      if (await thumbnailsDir.exists()) {
        await for (final entity in thumbnailsDir.list(recursive: true)) {
          if (entity is File) {
            totalThumbnailSize += await entity.length();
            thumbnailFileCount++;
          }
        }
      }

      // Calculate cache directory size
      final cacheDir = Directory(_cachePath);
      if (await cacheDir.exists()) {
        await for (final entity in cacheDir.list(recursive: true)) {
          if (entity is File) {
            totalCacheSize += await entity.length();
          }
        }
      }

      return StorageInfo(
        totalMusicSize: totalMusicSize,
        totalThumbnailSize: totalThumbnailSize,
        totalCacheSize: totalCacheSize,
        musicFileCount: musicFileCount,
        thumbnailFileCount: thumbnailFileCount,
      );
    } catch (e) {
      debugPrint('Failed to get storage info: $e');
      return StorageInfo(
        totalMusicSize: 0,
        totalThumbnailSize: 0,
        totalCacheSize: 0,
        musicFileCount: 0,
        thumbnailFileCount: 0,
      );
    }
  }

  // Clear cache
  Future<bool> clearCache() async {
    try {
      final cacheDir = Directory(_cachePath);
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await _ensureDirectoryExists(_cachePath);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Failed to clear cache: $e');
      return false;
    }
  }

  // Clear all thumbnails
  Future<bool> clearThumbnails() async {
    try {
      final thumbnailsDir = Directory(_thumbnailsPath);
      if (await thumbnailsDir.exists()) {
        await thumbnailsDir.delete(recursive: true);
        await _ensureDirectoryExists(_thumbnailsPath);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Failed to clear thumbnails: $e');
      return false;
    }
  }

  // Database operations
  Future<void> clearAllData() async {
    try {
      await _songsBox?.clear();
      await _playlistsBox?.clear();
      await _artistsBox?.clear();
      await _albumsBox?.clear();
      await _downloadsBox?.clear();

      // Also clear all files
      await clearCache();
      await clearThumbnails();

      final musicDir = Directory(_musicPath);
      if (await musicDir.exists()) {
        await musicDir.delete(recursive: true);
        await _ensureDirectoryExists(_musicPath);
      }
    } catch (e) {
      debugPrint('Failed to clear all data: $e');
      rethrow;
    }
  }

  // Export/Import data
  Future<Map<String, dynamic>> exportData() async {
    try {
      return {
        'songs': _songsBox?.values.map((song) => song.toJson()).toList() ?? [],
        'playlists':
            _playlistsBox?.values
                .map((playlist) => playlist.toJson())
                .toList() ??
            [],
        'artists':
            _artistsBox?.values.map((artist) => artist.toJson()).toList() ?? [],
        'albums':
            _albumsBox?.values.map((album) => album.toJson()).toList() ?? [],
        'downloads':
            _downloadsBox?.values
                .map((download) => download.toJson())
                .toList() ??
            [],
        'exportDate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('Failed to export data: $e');
      rethrow;
    }
  }

  Future<void> importData(Map<String, dynamic> data) async {
    try {
      // Clear existing data
      await clearAllData();

      // Import songs
      if (data['songs'] != null) {
        for (final songJson in data['songs']) {
          final song = Song.fromJson(songJson);
          await _songsBox?.put(song.id, song);
        }
      }

      // Import playlists
      if (data['playlists'] != null) {
        for (final playlistJson in data['playlists']) {
          final playlist = Playlist.fromJson(playlistJson);
          await _playlistsBox?.put(playlist.id, playlist);
        }
      }

      // Import artists
      if (data['artists'] != null) {
        for (final artistJson in data['artists']) {
          final artist = Artist.fromJson(artistJson);
          await _artistsBox?.put(artist.id, artist);
        }
      }

      // Import albums
      if (data['albums'] != null) {
        for (final albumJson in data['albums']) {
          final album = Album.fromJson(albumJson);
          await _albumsBox?.put(album.id, album);
        }
      }

      // Import downloads
      if (data['downloads'] != null) {
        for (final downloadJson in data['downloads']) {
          final download = Download.fromJson(downloadJson);
          await _downloadsBox?.put(download.id, download);
        }
      }
    } catch (e) {
      debugPrint('Failed to import data: $e');
      rethrow;
    }
  }

  // Helper method to open box with retry mechanism
  Future<Box<T>> _openBoxWithRetry<T>(
    String boxName, {
    int maxRetries = 3,
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      try {
        if (Hive.isBoxOpen(boxName)) {
          await Hive.box<T>(boxName).close();
        }
        return await Hive.openBox<T>(boxName);
      } catch (e) {
        debugPrint('Attempt ${i + 1} to open box $boxName failed: $e');
        if (i == maxRetries - 1) rethrow;

        // Wait before retry
        await Future.delayed(Duration(milliseconds: 500 * (i + 1)));

        // Try to clear lock files
        await _clearLockFiles();
      }
    }
    throw Exception('Failed to open box $boxName after $maxRetries attempts');
  }

  // Helper method to safely close boxes
  Future<void> _closeBoxesSafely() async {
    try {
      final boxNames = ['songs', 'playlists', 'artists', 'albums', 'downloads'];
      for (final boxName in boxNames) {
        if (Hive.isBoxOpen(boxName)) {
          await Hive.box(boxName).close();
        }
      }
    } catch (e) {
      debugPrint('Error closing boxes: $e');
    }
  }

  // Helper method to clear lock files
  Future<void> _clearLockFiles() async {
    try {
      final hiveDir = Directory(_documentsPath);
      if (await hiveDir.exists()) {
        final lockFiles = hiveDir
            .listSync()
            .where((file) => file.path.endsWith('.lock'))
            .toList();

        for (final lockFile in lockFiles) {
          try {
            await lockFile.delete();
            debugPrint('Deleted lock file: ${lockFile.path}');
          } catch (e) {
            debugPrint('Failed to delete lock file ${lockFile.path}: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Error clearing lock files: $e');
    }
  }

  // Close all boxes
  Future<void> dispose() async {
    await _closeBoxesSafely();
  }
}

// Storage information class
class StorageInfo {
  final int totalMusicSize;
  final int totalThumbnailSize;
  final int totalCacheSize;
  final int musicFileCount;
  final int thumbnailFileCount;

  StorageInfo({
    required this.totalMusicSize,
    required this.totalThumbnailSize,
    required this.totalCacheSize,
    required this.musicFileCount,
    required this.thumbnailFileCount,
  });

  int get totalSize => totalMusicSize + totalThumbnailSize + totalCacheSize;

  String get formattedTotalSize => _formatBytes(totalSize);
  String get formattedMusicSize => _formatBytes(totalMusicSize);
  String get formattedThumbnailSize => _formatBytes(totalThumbnailSize);
  String get formattedCacheSize => _formatBytes(totalCacheSize);

  String _formatBytes(int bytes) {
    if (bytes >= 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    } else if (bytes >= 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else if (bytes >= 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '$bytes B';
    }
  }
}
