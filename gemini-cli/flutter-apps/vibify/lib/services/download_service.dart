import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/song.dart';
import '../models/download.dart';
import '../services/youtube_service.dart';
import '../services/storage_service.dart';

/// Service for handling music downloads with progress tracking
class DownloadService {
  static DownloadService? _instance;
  static DownloadService get instance => _instance ??= DownloadService._();
  DownloadService._();

  final Dio _dio = Dio();
  final Map<String, CancelToken> _cancelTokens = {};
  final Map<String, StreamController<Download>> _progressControllers = {};

  // Download queue management
  final List<Download> _downloadQueue = [];
  bool _isProcessingQueue = false;
  final int _maxConcurrentDownloads = 3;
  int _activeDownloads = 0;

  // Stream controllers for UI updates
  final StreamController<List<Download>> _queueController =
      StreamController.broadcast();
  final StreamController<Download> _downloadUpdateController =
      StreamController.broadcast();

  // Getters for streams
  Stream<List<Download>> get queueStream => _queueController.stream;
  Stream<Download> get downloadUpdateStream => _downloadUpdateController.stream;

  // Getters for current state
  List<Download> get downloadQueue => List.unmodifiable(_downloadQueue);
  bool get isProcessingQueue => _isProcessingQueue;
  int get activeDownloads => _activeDownloads;

  /// Initialize the download service
  Future<void> initialize() async {
    try {
      // Configure Dio
      _dio.options = BaseOptions(
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {'User-Agent': 'Vibify/1.0.0 (Music Player)'},
      );

      // Load existing downloads from storage
      await _loadExistingDownloads();

      debugPrint('DownloadService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize DownloadService: $e');
      rethrow;
    }
  }

  /// Add a song to the download queue
  Future<String> addToDownloadQueue(Song song) async {
    try {
      // Check if already downloading or downloaded
      final existingDownload = _downloadQueue.firstWhere(
        (d) => d.songId == song.id,
        orElse: () =>
            Download(id: '', songId: '', title: '', artist: '', youtubeUrl: ''),
      );

      if (existingDownload.id.isNotEmpty) {
        if (existingDownload.isCompleted) {
          throw Exception('Song is already downloaded');
        } else if (existingDownload.isActive) {
          throw Exception('Song is already being downloaded');
        }
      }

      // Create new download
      final download = Download(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        songId: song.id,
        title: song.title,
        artist: song.artist,
        youtubeUrl: song.youtubeUrl ?? '',
        thumbnailUrl: song.thumbnailUrl,
        duration: song.duration,
        status: DownloadStatus.pending,
      );

      // Add to queue and storage
      _downloadQueue.add(download);
      await StorageService.instance.downloadsBox.put(download.id, download);

      // Update streams
      _updateQueue();
      _updateDownload(download);

      // Start processing queue
      _processDownloadQueue();

      return download.id;
    } catch (e) {
      debugPrint('Failed to add to download queue: $e');
      rethrow;
    }
  }

  /// Cancel a download
  Future<void> cancelDownload(String downloadId) async {
    try {
      final download = _downloadQueue.firstWhere((d) => d.id == downloadId);

      // Cancel the network request
      final cancelToken = _cancelTokens[downloadId];
      if (cancelToken != null && !cancelToken.isCancelled) {
        cancelToken.cancel('Download cancelled by user');
      }

      // Update download status
      download.markAsCancelled();
      await StorageService.instance.downloadsBox.put(download.id, download);

      _updateDownload(download);
      _activeDownloads = (_activeDownloads - 1).clamp(
        0,
        _maxConcurrentDownloads,
      );

      // Continue processing queue
      _processDownloadQueue();
    } catch (e) {
      debugPrint('Failed to cancel download: $e');
    }
  }

  /// Pause a download
  Future<void> pauseDownload(String downloadId) async {
    try {
      final download = _downloadQueue.firstWhere((d) => d.id == downloadId);

      if (download.status == DownloadStatus.downloading) {
        // Cancel current request
        final cancelToken = _cancelTokens[downloadId];
        if (cancelToken != null && !cancelToken.isCancelled) {
          cancelToken.cancel('Download paused by user');
        }

        // Mark as paused
        download.markAsPaused();
        await StorageService.instance.downloadsBox.put(download.id, download);

        _updateDownload(download);
        _activeDownloads = (_activeDownloads - 1).clamp(
          0,
          _maxConcurrentDownloads,
        );
      }
    } catch (e) {
      debugPrint('Failed to pause download: $e');
    }
  }

  /// Resume a paused download
  Future<void> resumeDownload(String downloadId) async {
    try {
      final download = _downloadQueue.firstWhere((d) => d.id == downloadId);

      if (download.status == DownloadStatus.paused) {
        download.resume();
        await StorageService.instance.downloadsBox.put(download.id, download);

        _updateDownload(download);
        _processDownloadQueue();
      }
    } catch (e) {
      debugPrint('Failed to resume download: $e');
    }
  }

  /// Retry a failed download
  Future<void> retryDownload(String downloadId) async {
    try {
      final download = _downloadQueue.firstWhere((d) => d.id == downloadId);

      if (download.canRetry) {
        // Reset download state
        final retryDownload = download.copyWith(
          status: DownloadStatus.pending,
          progress: 0.0,
          downloadedBytes: 0,
          errorMessage: null,
        );

        // Update in queue and storage
        final index = _downloadQueue.indexWhere((d) => d.id == downloadId);
        _downloadQueue[index] = retryDownload;
        await StorageService.instance.downloadsBox.put(
          retryDownload.id,
          retryDownload,
        );

        _updateDownload(retryDownload);
        _processDownloadQueue();
      }
    } catch (e) {
      debugPrint('Failed to retry download: $e');
    }
  }

  /// Remove a download from queue and delete file if exists
  Future<void> removeDownload(String downloadId) async {
    try {
      final download = _downloadQueue.firstWhere((d) => d.id == downloadId);

      // Cancel if active
      if (download.isActive) {
        await cancelDownload(downloadId);
      }

      // Delete file if exists
      if (download.localPath != null) {
        final file = File(download.localPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // Remove from queue and storage
      _downloadQueue.removeWhere((d) => d.id == downloadId);
      await StorageService.instance.downloadsBox.delete(downloadId);

      // Close progress controller
      _progressControllers[downloadId]?.close();
      _progressControllers.remove(downloadId);

      _updateQueue();
    } catch (e) {
      debugPrint('Failed to remove download: $e');
    }
  }

  /// Get download progress stream for a specific download
  Stream<Download>? getDownloadProgressStream(String downloadId) {
    if (!_progressControllers.containsKey(downloadId)) {
      _progressControllers[downloadId] = StreamController<Download>.broadcast();
    }
    return _progressControllers[downloadId]?.stream;
  }

  /// Process the download queue
  Future<void> _processDownloadQueue() async {
    if (_isProcessingQueue || _activeDownloads >= _maxConcurrentDownloads) {
      return;
    }

    _isProcessingQueue = true;

    try {
      // Find next pending download
      final pendingDownloads = _downloadQueue
          .where((d) => d.status == DownloadStatus.pending)
          .toList();

      for (final download in pendingDownloads) {
        if (_activeDownloads >= _maxConcurrentDownloads) break;

        _activeDownloads++;
        _downloadSong(download);
      }
    } finally {
      _isProcessingQueue = false;
    }
  }

  /// Download a single song
  Future<void> _downloadSong(Download download) async {
    try {
      // Mark as downloading
      download.markAsStarted();
      await StorageService.instance.downloadsBox.put(download.id, download);
      _updateDownload(download);

      // Get audio stream URL
      String? audioUrl;
      if (download.youtubeUrl.isNotEmpty) {
        final videoId = YouTubeService.instance.extractVideoId(
          download.youtubeUrl,
        );
        if (videoId != null) {
          audioUrl = await YouTubeService.instance.getAudioStreamUrl(videoId);
        }
      }

      if (audioUrl == null) {
        throw Exception('Could not get audio stream URL');
      }

      // Create cancel token
      final cancelToken = CancelToken();
      _cancelTokens[download.id] = cancelToken;

      // Prepare file path
      final fileName = '${download.songId}.m4a';
      final filePath = '${StorageService.instance.musicPath}/$fileName';

      // Download file with progress tracking
      await _dio.download(
        audioUrl,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total > 0) {
            final progress = received / total;
            download.updateProgress(
              progress,
              downloaded: received,
              total: total,
            );
            _updateDownload(download);
            _progressControllers[download.id]?.add(download);
          }
        },
      );

      // Mark as completed
      download.markAsCompleted(filePath);
      await StorageService.instance.downloadsBox.put(download.id, download);

      // Update song in library
      await _updateSongWithDownload(download, filePath);

      _updateDownload(download);
      _progressControllers[download.id]?.add(download);
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.cancel) {
        // Download was cancelled, don't mark as failed
        debugPrint('Download cancelled: ${download.title}');
      } else {
        // Mark as failed
        download.markAsFailed(e.toString());
        await StorageService.instance.downloadsBox.put(download.id, download);
        _updateDownload(download);
        debugPrint('Download failed: ${download.title} - $e');
      }
    } finally {
      // Clean up
      _cancelTokens.remove(download.id);
      _activeDownloads = (_activeDownloads - 1).clamp(
        0,
        _maxConcurrentDownloads,
      );

      // Continue processing queue
      _processDownloadQueue();
    }
  }

  /// Update song in library with download information
  Future<void> _updateSongWithDownload(
    Download download,
    String filePath,
  ) async {
    try {
      final songsBox = StorageService.instance.songsBox;
      final song = songsBox.get(download.songId);

      if (song != null) {
        final updatedSong = song.copyWith(
          localPath: filePath,
          isDownloaded: true,
          fileSize: await File(filePath).length(),
        );

        await songsBox.put(song.id, updatedSong);
      }
    } catch (e) {
      debugPrint('Failed to update song with download info: $e');
    }
  }

  /// Load existing downloads from storage
  Future<void> _loadExistingDownloads() async {
    try {
      final downloads = StorageService.instance.downloadsBox.values.toList();
      _downloadQueue.clear();
      _downloadQueue.addAll(downloads);

      // Resume any pending downloads
      final pendingDownloads = downloads
          .where(
            (d) =>
                d.status == DownloadStatus.pending ||
                d.status == DownloadStatus.downloading,
          )
          .toList();

      for (final download in pendingDownloads) {
        download.status = DownloadStatus.pending;
        await StorageService.instance.downloadsBox.put(download.id, download);
      }

      _updateQueue();
    } catch (e) {
      debugPrint('Failed to load existing downloads: $e');
    }
  }

  /// Update queue stream
  void _updateQueue() {
    _queueController.add(List.from(_downloadQueue));
  }

  /// Update download stream
  void _updateDownload(Download download) {
    _downloadUpdateController.add(download);
  }

  /// Get download statistics
  Map<String, int> getDownloadStats() {
    final completed = _downloadQueue.where((d) => d.isCompleted).length;
    final active = _downloadQueue.where((d) => d.isActive).length;
    final failed = _downloadQueue.where((d) => d.isFailed).length;
    final pending = _downloadQueue
        .where((d) => d.status == DownloadStatus.pending)
        .length;

    return {
      'total': _downloadQueue.length,
      'completed': completed,
      'active': active,
      'failed': failed,
      'pending': pending,
    };
  }

  /// Clear completed downloads
  Future<void> clearCompletedDownloads() async {
    try {
      final completedDownloads = _downloadQueue
          .where((d) => d.isCompleted)
          .toList();

      for (final download in completedDownloads) {
        await StorageService.instance.downloadsBox.delete(download.id);
        _progressControllers[download.id]?.close();
        _progressControllers.remove(download.id);
      }

      _downloadQueue.removeWhere((d) => d.isCompleted);
      _updateQueue();
    } catch (e) {
      debugPrint('Failed to clear completed downloads: $e');
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    // Cancel all active downloads
    for (final cancelToken in _cancelTokens.values) {
      if (!cancelToken.isCancelled) {
        cancelToken.cancel('Service disposing');
      }
    }

    // Close all controllers
    await _queueController.close();
    await _downloadUpdateController.close();

    for (final controller in _progressControllers.values) {
      await controller.close();
    }

    _dio.close();
  }
}
