# Services Directory

This directory contains all service classes for business logic and external integrations.

## Planned Services:

- `audio_service.dart` - Audio playback management using just_audio
- `youtube_service.dart` - YouTube search and streaming using youtube_explode_dart
- `download_service.dart` - File download management using dio and flutter_downloader
- `storage_service.dart` - Local storage management using Hive and path_provider
- `permission_service.dart` - Permission handling using permission_handler
- `background_service.dart` - Background audio service using audio_service

All services will follow:
- Singleton pattern where appropriate
- Proper error handling and logging
- Async/await patterns
- Stream-based data flow where needed
