import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/song.dart';

import '../services/youtube_service.dart';

/// Service for handling music search with caching and suggestions
class SearchService {
  static SearchService? _instance;
  static SearchService get instance => _instance ??= SearchService._();
  SearchService._();

  final YouTubeService _youtubeService = YouTubeService.instance;

  // Search state
  final Map<String, List<Song>> _searchCache = {};
  final List<String> _searchHistory = [];
  final List<String> _popularSearches = [
    'trending music',
    'pop hits',
    'rock classics',
    'hip hop',
    'electronic music',
    'indie songs',
    'acoustic covers',
    'jazz standards',
  ];

  // Stream controllers for real-time updates
  final StreamController<List<Song>> _searchResultsController =
      StreamController.broadcast();
  final StreamController<List<String>> _suggestionsController =
      StreamController.broadcast();
  final StreamController<bool> _loadingController =
      StreamController.broadcast();

  // Getters for streams
  Stream<List<Song>> get searchResultsStream => _searchResultsController.stream;
  Stream<List<String>> get suggestionsStream => _suggestionsController.stream;
  Stream<bool> get loadingStream => _loadingController.stream;

  // Current state
  String _currentQuery = '';
  List<Song> _currentResults = [];
  bool _isLoading = false;
  Timer? _debounceTimer;

  // Getters
  String get currentQuery => _currentQuery;
  List<Song> get currentResults => List.unmodifiable(_currentResults);
  List<String> get searchHistory => List.unmodifiable(_searchHistory);
  List<String> get popularSearches => List.unmodifiable(_popularSearches);
  bool get isLoading => _isLoading;

  /// Initialize the search service
  Future<void> initialize() async {
    try {
      await _loadSearchHistory();
      debugPrint('SearchService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize SearchService: $e');
    }
  }

  /// Search for music with debouncing
  Future<void> searchMusic(String query, {bool immediate = false}) async {
    if (query.trim().isEmpty) {
      _clearResults();
      return;
    }

    _currentQuery = query.trim();

    // Cancel previous timer
    _debounceTimer?.cancel();

    if (immediate) {
      await _performSearch(query);
    } else {
      // Debounce search to avoid too many API calls
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        _performSearch(query);
      });
    }
  }

  /// Perform the actual search
  Future<void> _performSearch(String query) async {
    try {
      _setLoading(true);

      // Check cache first
      if (_searchCache.containsKey(query)) {
        _currentResults = _searchCache[query]!;
        _searchResultsController.add(_currentResults);
        _setLoading(false);
        return;
      }

      // Search YouTube
      final results = await _youtubeService.searchMusic(query, maxResults: 50);

      // Cache results
      _searchCache[query] = results;
      _currentResults = results;

      // Add to search history
      await _addToSearchHistory(query);

      // Update streams
      _searchResultsController.add(_currentResults);
      _setLoading(false);
    } catch (e) {
      debugPrint('Search failed: $e');
      _setLoading(false);
      _searchResultsController.addError(e);
    }
  }

  /// Get search suggestions based on query
  List<String> getSuggestions(String query) {
    if (query.trim().isEmpty) {
      return [..._searchHistory.take(5), ..._popularSearches.take(3)];
    }

    final suggestions = <String>[];
    final lowerQuery = query.toLowerCase();

    // Add matching history items
    for (final historyItem in _searchHistory) {
      if (historyItem.toLowerCase().contains(lowerQuery)) {
        suggestions.add(historyItem);
      }
    }

    // Add matching popular searches
    for (final popularSearch in _popularSearches) {
      if (popularSearch.toLowerCase().contains(lowerQuery) &&
          !suggestions.contains(popularSearch)) {
        suggestions.add(popularSearch);
      }
    }

    // Add query variations
    if (!suggestions.contains(query)) {
      suggestions.insert(0, query);
    }

    return suggestions.take(8).toList();
  }

  /// Get trending music
  Future<List<Song>> getTrendingMusic() async {
    try {
      _setLoading(true);
      final results = await _youtubeService.getTrendingVideos(maxResults: 20);
      _setLoading(false);
      return results;
    } catch (e) {
      debugPrint('Failed to get trending music: $e');
      _setLoading(false);
      return [];
    }
  }

  /// Get music by genre
  Future<List<Song>> getMusicByGenre(String genre) async {
    try {
      _setLoading(true);
      final query = '$genre music songs';
      final results = await _youtubeService.searchMusic(query, maxResults: 30);
      _setLoading(false);
      return results;
    } catch (e) {
      debugPrint('Failed to get music by genre: $e');
      _setLoading(false);
      return [];
    }
  }

  /// Get related songs
  Future<List<Song>> getRelatedSongs(Song song) async {
    try {
      if (song.youtubeUrl == null) return [];

      final videoId = _youtubeService.extractVideoId(song.youtubeUrl!);
      if (videoId == null) return [];

      return await _youtubeService.getRelatedVideos(videoId, maxResults: 20);
    } catch (e) {
      debugPrint('Failed to get related songs: $e');
      return [];
    }
  }

  /// Search by artist
  Future<List<Song>> searchByArtist(String artist) async {
    try {
      _setLoading(true);
      final query = '$artist songs music';
      final results = await _youtubeService.searchMusic(query, maxResults: 30);
      _setLoading(false);
      return results;
    } catch (e) {
      debugPrint('Failed to search by artist: $e');
      _setLoading(false);
      return [];
    }
  }

  /// Search similar songs
  Future<List<Song>> searchSimilar(String title, String artist) async {
    try {
      _setLoading(true);
      final query = 'similar to $title $artist music';
      final results = await _youtubeService.searchMusic(query, maxResults: 20);
      _setLoading(false);
      return results;
    } catch (e) {
      debugPrint('Failed to search similar songs: $e');
      _setLoading(false);
      return [];
    }
  }

  /// Get search suggestions with autocomplete
  Future<List<String>> getAutocompleteSuggestions(String query) async {
    try {
      // For now, return local suggestions
      // In a real app, you might call a suggestions API
      return getSuggestions(query);
    } catch (e) {
      debugPrint('Failed to get autocomplete suggestions: $e');
      return getSuggestions(query);
    }
  }

  /// Add query to search history
  Future<void> _addToSearchHistory(String query) async {
    try {
      // Remove if already exists
      _searchHistory.remove(query);

      // Add to beginning
      _searchHistory.insert(0, query);

      // Keep only last 20 searches
      if (_searchHistory.length > 20) {
        _searchHistory.removeRange(20, _searchHistory.length);
      }

      // Save to storage
      await _saveSearchHistory();
    } catch (e) {
      debugPrint('Failed to add to search history: $e');
    }
  }

  /// Clear search results
  void _clearResults() {
    _currentQuery = '';
    _currentResults.clear();
    _searchResultsController.add(_currentResults);
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    _loadingController.add(_isLoading);
  }

  /// Clear search history
  Future<void> clearSearchHistory() async {
    try {
      _searchHistory.clear();
      await _saveSearchHistory();
    } catch (e) {
      debugPrint('Failed to clear search history: $e');
    }
  }

  /// Remove item from search history
  Future<void> removeFromSearchHistory(String query) async {
    try {
      _searchHistory.remove(query);
      await _saveSearchHistory();
    } catch (e) {
      debugPrint('Failed to remove from search history: $e');
    }
  }

  /// Load search history from storage
  Future<void> _loadSearchHistory() async {
    try {
      // For now, we'll skip persistent search history
      // In a real app, you'd want a dedicated settings box
      debugPrint('Search history loaded (in-memory only)');
    } catch (e) {
      debugPrint('Failed to load search history: $e');
    }
  }

  /// Save search history to storage
  Future<void> _saveSearchHistory() async {
    try {
      // For now, we'll skip persistent search history
      // In a real app, you'd want a dedicated settings box
      debugPrint('Search history saved (in-memory only)');
    } catch (e) {
      debugPrint('Failed to save search history: $e');
    }
  }

  /// Clear search cache
  void clearCache() {
    _searchCache.clear();
  }

  /// Get cache size
  int getCacheSize() {
    return _searchCache.length;
  }

  /// Get popular genres
  List<String> getPopularGenres() {
    return [
      'Pop',
      'Rock',
      'Hip Hop',
      'Electronic',
      'Jazz',
      'Classical',
      'Country',
      'R&B',
      'Indie',
      'Alternative',
      'Folk',
      'Reggae',
    ];
  }

  /// Get mood-based searches
  List<String> getMoodSearches() {
    return [
      'happy music',
      'sad songs',
      'energetic music',
      'relaxing music',
      'workout songs',
      'study music',
      'party music',
      'romantic songs',
    ];
  }

  /// Dispose resources
  Future<void> dispose() async {
    _debounceTimer?.cancel();
    await _searchResultsController.close();
    await _suggestionsController.close();
    await _loadingController.close();
  }
}
