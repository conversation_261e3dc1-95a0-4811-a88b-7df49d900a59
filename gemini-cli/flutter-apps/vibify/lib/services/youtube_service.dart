import 'package:flutter/foundation.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import '../models/song.dart';

/// Service for YouTube integration and audio extraction
class YouTubeService {
  static YouTubeService? _instance;
  static YouTubeService get instance => _instance ??= YouTubeService._();
  YouTubeService._();

  final YoutubeExplode _youtubeExplode = YoutubeExplode();

  /// Search for videos on YouTube
  Future<List<Song>> searchVideos(String query, {int maxResults = 20}) async {
    try {
      final searchResults = await _youtubeExplode.search.search(query);
      final videos = searchResults.take(maxResults).toList();

      final songs = <Song>[];
      for (final video in videos) {
        final song = Song.fromYoutube(
          id: video.id.value,
          title: video.title,
          artist: video.author,
          thumbnailUrl: video.thumbnails.mediumResUrl,
          duration: video.duration,
          youtubeUrl: video.url,
        );
        songs.add(song);
      }

      return songs;
    } catch (e) {
      debugPrint('Failed to search YouTube: $e');
      return [];
    }
  }

  /// Get video details by ID
  Future<Song?> getVideoDetails(String videoId) async {
    try {
      final video = await _youtubeExplode.videos.get(videoId);

      return Song.fromYoutube(
        id: video.id.value,
        title: video.title,
        artist: video.author,
        thumbnailUrl: video.thumbnails.mediumResUrl,
        duration: video.duration,
        youtubeUrl: video.url,
      );
    } catch (e) {
      debugPrint('Failed to get video details: $e');
      return null;
    }
  }

  /// Get audio stream URL for a video
  Future<String?> getAudioStreamUrl(String videoId) async {
    try {
      final manifest = await _youtubeExplode.videos.streamsClient.getManifest(
        videoId,
      );

      // Get the best audio-only stream
      final audioStreams = manifest.audioOnly;
      if (audioStreams.isNotEmpty) {
        // Sort by bitrate and get the best quality
        audioStreams.sort(
          (a, b) => b.bitrate.bitsPerSecond.compareTo(a.bitrate.bitsPerSecond),
        );
        return audioStreams.first.url.toString();
      }

      // Fallback to muxed streams if no audio-only available
      final muxedStreams = manifest.muxed;
      if (muxedStreams.isNotEmpty) {
        // Sort by bitrate and get the best quality
        muxedStreams.sort(
          (a, b) => b.bitrate.bitsPerSecond.compareTo(a.bitrate.bitsPerSecond),
        );
        return muxedStreams.first.url.toString();
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get audio stream URL: $e');
      return null;
    }
  }

  /// Get video thumbnail URL
  Future<String?> getThumbnailUrl(
    String videoId, {
    ThumbnailQuality quality = ThumbnailQuality.medium,
  }) async {
    try {
      final video = await _youtubeExplode.videos.get(videoId);

      switch (quality) {
        case ThumbnailQuality.low:
          return video.thumbnails.lowResUrl;
        case ThumbnailQuality.medium:
          return video.thumbnails.mediumResUrl;
        case ThumbnailQuality.high:
          return video.thumbnails.highResUrl;
        case ThumbnailQuality.maxRes:
          return video.thumbnails.maxResUrl;
      }
    } catch (e) {
      debugPrint('Failed to get thumbnail URL: $e');
      return null;
    }
  }

  /// Get playlist videos
  Future<List<Song>> getPlaylistVideos(String playlistId) async {
    try {
      final videos = await _youtubeExplode.playlists
          .getVideos(playlistId)
          .toList();

      final songs = <Song>[];
      for (final video in videos) {
        final song = Song.fromYoutube(
          id: video.id.value,
          title: video.title,
          artist: video.author,
          thumbnailUrl: video.thumbnails.mediumResUrl,
          duration: video.duration,
          youtubeUrl: video.url,
        );
        songs.add(song);
      }

      return songs;
    } catch (e) {
      debugPrint('Failed to get playlist videos: $e');
      return [];
    }
  }

  /// Extract video ID from YouTube URL
  String? extractVideoId(String url) {
    try {
      return VideoId.parseVideoId(url);
    } catch (e) {
      debugPrint('Failed to extract video ID: $e');
      return null;
    }
  }

  /// Check if URL is a valid YouTube URL
  bool isValidYouTubeUrl(String url) {
    try {
      VideoId.parseVideoId(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get trending videos
  Future<List<Song>> getTrendingVideos({int maxResults = 20}) async {
    try {
      // YouTube Explode doesn't have a direct trending endpoint
      // So we'll search for popular music terms
      final queries = ['music', 'songs', 'hits', 'popular music'];
      final allSongs = <Song>[];

      for (final query in queries) {
        final songs = await searchVideos(
          query,
          maxResults: maxResults ~/ queries.length,
        );
        allSongs.addAll(songs);
      }

      return allSongs.take(maxResults).toList();
    } catch (e) {
      debugPrint('Failed to get trending videos: $e');
      return [];
    }
  }

  /// Get related videos
  Future<List<Song>> getRelatedVideos(
    String videoId, {
    int maxResults = 10,
  }) async {
    try {
      // Get video details first
      final video = await _youtubeExplode.videos.get(videoId);

      // Search for similar content using the video title and author
      final searchQuery = '${video.author} ${video.title}';
      final relatedSongs = await searchVideos(
        searchQuery,
        maxResults: maxResults + 5,
      );

      // Filter out the original video and return limited results
      final filtered = relatedSongs
          .where((song) => song.id != videoId)
          .take(maxResults)
          .toList();

      return filtered;
    } catch (e) {
      debugPrint('Failed to get related videos: $e');
      return [];
    }
  }

  /// Download video metadata for offline use
  Future<Map<String, dynamic>?> downloadVideoMetadata(String videoId) async {
    try {
      final video = await _youtubeExplode.videos.get(videoId);

      return {
        'id': video.id.value,
        'title': video.title,
        'author': video.author,
        'description': video.description,
        'duration': video.duration?.inMilliseconds,
        'uploadDate': video.uploadDate?.toIso8601String(),
        'viewCount': video.engagement.viewCount,
        'likeCount': video.engagement.likeCount,
        'thumbnails': {
          'low': video.thumbnails.lowResUrl,
          'medium': video.thumbnails.mediumResUrl,
          'high': video.thumbnails.highResUrl,
          'maxRes': video.thumbnails.maxResUrl,
        },
        'keywords': video.keywords,
      };
    } catch (e) {
      debugPrint('Failed to download video metadata: $e');
      return null;
    }
  }

  /// Get channel videos
  Future<List<Song>> getChannelVideos(
    String channelId, {
    int maxResults = 20,
  }) async {
    try {
      final videos = await _youtubeExplode.channels
          .getUploads(channelId)
          .take(maxResults)
          .toList();

      final songs = <Song>[];
      for (final video in videos) {
        final song = Song.fromYoutube(
          id: video.id.value,
          title: video.title,
          artist: video.author,
          thumbnailUrl: video.thumbnails.mediumResUrl,
          duration: video.duration,
          youtubeUrl: video.url,
        );
        songs.add(song);
      }

      return songs;
    } catch (e) {
      debugPrint('Failed to get channel videos: $e');
      return [];
    }
  }

  /// Search for music specifically
  Future<List<Song>> searchMusic(String query, {int maxResults = 20}) async {
    try {
      // Add music-specific terms to improve results
      final musicQuery = '$query music song audio';
      return await searchVideos(musicQuery, maxResults: maxResults);
    } catch (e) {
      debugPrint('Failed to search music: $e');
      return [];
    }
  }

  /// Get video quality information
  Future<Map<String, dynamic>?> getVideoQualityInfo(String videoId) async {
    try {
      final manifest = await _youtubeExplode.videos.streamsClient.getManifest(
        videoId,
      );

      final audioStreams = manifest.audioOnly
          .map(
            (stream) => {
              'itag': stream.tag,
              'bitrate': stream.bitrate.bitsPerSecond,
              'container': stream.container.name,
              'codec': stream.audioCodec,
              'size': stream.size.totalBytes,
            },
          )
          .toList();

      final videoStreams = manifest.muxed
          .map(
            (stream) => {
              'itag': stream.tag,
              'quality': stream.videoQuality.toString(),
              'bitrate': stream.bitrate.bitsPerSecond,
              'container': stream.container.name,
              'size': stream.size.totalBytes,
            },
          )
          .toList();

      return {'audioStreams': audioStreams, 'videoStreams': videoStreams};
    } catch (e) {
      debugPrint('Failed to get video quality info: $e');
      return null;
    }
  }

  /// Dispose resources
  void dispose() {
    _youtubeExplode.close();
  }
}

/// Thumbnail quality options
enum ThumbnailQuality { low, medium, high, maxRes }
