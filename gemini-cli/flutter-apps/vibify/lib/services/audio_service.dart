import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_service/audio_service.dart';
import '../models/song.dart';

/// Audio service for handling music playback with background support
class VibifyAudioService extends BaseAudioHandler {
  static VibifyAudioService? _instance;
  static VibifyAudioService get instance =>
      _instance ??= VibifyAudioService._();
  VibifyAudioService._();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final List<Song> _queue = [];
  int _currentIndex = 0;

  // Playback state
  bool _isShuffleEnabled = false;
  AudioServiceRepeatMode _repeatMode = AudioServiceRepeatMode.none;

  // Stream controllers for state updates
  final StreamController<List<Song>> _queueController =
      StreamController.broadcast();
  final StreamController<int> _currentIndexController =
      StreamController.broadcast();
  final StreamController<bool> _shuffleController =
      StreamController.broadcast();
  final StreamController<AudioServiceRepeatMode> _repeatController =
      StreamController.broadcast();

  // Getters for streams
  Stream<List<Song>> get queueStream => _queueController.stream;
  Stream<int> get currentIndexStream => _currentIndexController.stream;
  Stream<bool> get shuffleStream => _shuffleController.stream;
  Stream<AudioServiceRepeatMode> get repeatStream => _repeatController.stream;

  // Getters for current state
  List<Song> get songQueue => List.unmodifiable(_queue);
  int get currentIndex => _currentIndex;
  Song? get currentSong => _queue.isNotEmpty ? _queue[_currentIndex] : null;
  bool get isShuffleEnabled => _isShuffleEnabled;
  AudioServiceRepeatMode get repeatMode => _repeatMode;

  // Audio player streams
  Stream<Duration> get positionStream => _audioPlayer.positionStream;
  Stream<Duration?> get durationStream => _audioPlayer.durationStream;
  Stream<PlayerState> get playerStateStream => _audioPlayer.playerStateStream;
  Stream<double> get volumeStream => _audioPlayer.volumeStream;
  Stream<double> get speedStream => _audioPlayer.speedStream;

  /// Initialize the audio service
  Future<void> initialize() async {
    try {
      // Initialize audio service
      _instance = await AudioService.init(
        builder: () => VibifyAudioService._(),
        config: const AudioServiceConfig(
          androidNotificationChannelId: 'com.vibify.audio',
          androidNotificationChannelName: 'Vibify Audio',
          androidNotificationOngoing: true,
          androidShowNotificationBadge: true,
          androidNotificationChannelDescription: 'Vibify music playback',
          androidNotificationIcon: 'drawable/ic_notification',
          androidStopForegroundOnPause: true,
        ),
      );

      // Listen to audio player state changes
      _audioPlayer.playerStateStream.listen(_handlePlayerStateChange);
      _audioPlayer.positionStream.listen(_handlePositionChange);
      _audioPlayer.durationStream.listen(_handleDurationChange);
      _audioPlayer.currentIndexStream.listen(_handleIndexChange);

      debugPrint('VibifyAudioService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize VibifyAudioService: $e');
      rethrow;
    }
  }

  /// Play a single song
  Future<void> playSong(Song song) async {
    try {
      _queue.clear();
      _queue.add(song);
      _currentIndex = 0;

      await _loadCurrentSong();
      await play();

      _updateQueue();
      _updateMediaItem();
    } catch (e) {
      debugPrint('Failed to play song: $e');
      rethrow;
    }
  }

  /// Play a playlist starting from a specific index
  Future<void> playPlaylist(List<Song> songs, {int startIndex = 0}) async {
    try {
      if (songs.isEmpty) return;

      _queue.clear();
      _queue.addAll(songs);
      _currentIndex = startIndex.clamp(0, songs.length - 1);

      await _loadCurrentSong();
      await play();

      _updateQueue();
      _updateMediaItem();
    } catch (e) {
      debugPrint('Failed to play playlist: $e');
      rethrow;
    }
  }

  /// Add song to queue
  Future<void> addToQueue(Song song) async {
    _queue.add(song);
    _updateQueue();
  }

  /// Add multiple songs to queue
  Future<void> addSongsToQueue(List<Song> songs) async {
    _queue.addAll(songs);
    _updateQueue();
  }

  /// Remove song from queue
  Future<void> removeFromQueue(int index) async {
    if (index < 0 || index >= _queue.length) return;

    _queue.removeAt(index);

    if (index < _currentIndex) {
      _currentIndex--;
    } else if (index == _currentIndex) {
      if (_queue.isNotEmpty) {
        _currentIndex = _currentIndex.clamp(0, _queue.length - 1);
        await _loadCurrentSong();
      } else {
        await stop();
      }
    }

    _updateQueue();
    _updateCurrentIndex();
  }

  /// Clear the entire queue
  Future<void> clearQueue() async {
    _queue.clear();
    _currentIndex = 0;
    await stop();
    _updateQueue();
    _updateCurrentIndex();
  }

  /// Skip to next song
  @override
  Future<void> skipToNext() async {
    if (_queue.isEmpty) return;

    if (_repeatMode == AudioServiceRepeatMode.one) {
      await seek(Duration.zero);
      await play();
      return;
    }

    if (_currentIndex < _queue.length - 1) {
      _currentIndex++;
    } else if (_repeatMode == AudioServiceRepeatMode.all) {
      _currentIndex = 0;
    } else {
      return; // No next song available
    }

    await _loadCurrentSong();
    await play();
    _updateCurrentIndex();
    _updateMediaItem();
  }

  /// Skip to previous song
  @override
  Future<void> skipToPrevious() async {
    if (_queue.isEmpty) return;

    // If more than 3 seconds have passed, restart current song
    final position = _audioPlayer.position;
    if (position.inSeconds > 3) {
      await seek(Duration.zero);
      return;
    }

    if (_currentIndex > 0) {
      _currentIndex--;
    } else if (_repeatMode == AudioServiceRepeatMode.all) {
      _currentIndex = _queue.length - 1;
    } else {
      await seek(Duration.zero);
      return;
    }

    await _loadCurrentSong();
    await play();
    _updateCurrentIndex();
    _updateMediaItem();
  }

  /// Skip to specific queue index
  @override
  Future<void> skipToQueueItem(int index) async {
    if (index < 0 || index >= _queue.length) return;

    _currentIndex = index;
    await _loadCurrentSong();
    await play();
    _updateCurrentIndex();
    _updateMediaItem();
  }

  /// Play/resume playback
  @override
  Future<void> play() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      debugPrint('Failed to play: $e');
      rethrow;
    }
  }

  /// Pause playback
  @override
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      debugPrint('Failed to pause: $e');
      rethrow;
    }
  }

  /// Stop playback
  @override
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      playbackState.add(
        playbackState.value.copyWith(
          playing: false,
          processingState: AudioProcessingState.idle,
        ),
      );
    } catch (e) {
      debugPrint('Failed to stop: $e');
      rethrow;
    }
  }

  /// Seek to position
  @override
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      debugPrint('Failed to seek: $e');
      rethrow;
    }
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      debugPrint('Failed to set volume: $e');
      rethrow;
    }
  }

  /// Set playback speed
  @override
  Future<void> setSpeed(double speed) async {
    try {
      await _audioPlayer.setSpeed(speed.clamp(0.25, 3.0));
    } catch (e) {
      debugPrint('Failed to set speed: $e');
      rethrow;
    }
  }

  /// Toggle shuffle mode
  @override
  Future<void> setShuffleMode(AudioServiceShuffleMode shuffleMode) async {
    _isShuffleEnabled = shuffleMode == AudioServiceShuffleMode.all;
    _shuffleController.add(_isShuffleEnabled);

    // TODO: Implement queue shuffling logic

    playbackState.add(playbackState.value.copyWith(shuffleMode: shuffleMode));
  }

  /// Set repeat mode
  @override
  Future<void> setRepeatMode(AudioServiceRepeatMode repeatMode) async {
    _repeatMode = repeatMode;
    _repeatController.add(_repeatMode);

    playbackState.add(playbackState.value.copyWith(repeatMode: repeatMode));
  }

  /// Load current song into audio player
  Future<void> _loadCurrentSong() async {
    if (_queue.isEmpty || _currentIndex >= _queue.length) return;

    final song = _queue[_currentIndex];
    String? audioSource;

    // Determine audio source
    if (song.localPath != null) {
      audioSource = song.localPath!;
    } else if (song.youtubeUrl != null) {
      // TODO: Extract audio URL from YouTube
      audioSource = song.youtubeUrl!;
    }

    if (audioSource != null) {
      try {
        await _audioPlayer.setUrl(audioSource);
      } catch (e) {
        debugPrint('Failed to load audio source: $e');
        // Try next song if current fails
        if (_currentIndex < _queue.length - 1) {
          _currentIndex++;
          await _loadCurrentSong();
        }
      }
    }
  }

  /// Update media item for system notification
  void _updateMediaItem() {
    if (currentSong == null) return;

    final song = currentSong!;
    mediaItem.add(
      MediaItem(
        id: song.id,
        title: song.title,
        artist: song.artist,
        album: song.album,
        duration: song.duration,
        artUri: song.primaryImageUrl != null
            ? Uri.parse(song.primaryImageUrl!)
            : null,
      ),
    );
  }

  /// Handle player state changes
  void _handlePlayerStateChange(PlayerState state) {
    final isPlaying = state.playing;
    final processingState = _mapProcessingState(state.processingState);

    playbackState.add(
      PlaybackState(
        controls: [
          MediaControl.skipToPrevious,
          isPlaying ? MediaControl.pause : MediaControl.play,
          MediaControl.skipToNext,
          MediaControl.stop,
        ],
        systemActions: const {
          MediaAction.seek,
          MediaAction.seekForward,
          MediaAction.seekBackward,
        },
        androidCompactActionIndices: const [0, 1, 2],
        processingState: processingState,
        playing: isPlaying,
        updatePosition: _audioPlayer.position,
        bufferedPosition: _audioPlayer.bufferedPosition,
        speed: _audioPlayer.speed,
        queueIndex: _currentIndex,
        shuffleMode: _isShuffleEnabled
            ? AudioServiceShuffleMode.all
            : AudioServiceShuffleMode.none,
        repeatMode: _repeatMode,
      ),
    );

    // Handle song completion
    if (state.processingState == ProcessingState.completed) {
      _handleSongCompleted();
    }
  }

  /// Handle position changes
  void _handlePositionChange(Duration position) {
    playbackState.add(playbackState.value.copyWith(updatePosition: position));
  }

  /// Handle duration changes
  void _handleDurationChange(Duration? duration) {
    if (duration != null && currentSong != null) {
      // Update song duration if not set
      final song = currentSong!;
      if (song.duration == null) {
        final updatedSong = song.copyWith(duration: duration);
        _queue[_currentIndex] = updatedSong;
        _updateMediaItem();
      }
    }
  }

  /// Handle index changes
  void _handleIndexChange(int? index) {
    if (index != null && index != _currentIndex) {
      _currentIndex = index;
      _updateCurrentIndex();
      _updateMediaItem();
    }
  }

  /// Handle song completion
  void _handleSongCompleted() {
    if (_repeatMode == AudioServiceRepeatMode.one) {
      seek(Duration.zero);
      play();
    } else {
      skipToNext();
    }
  }

  /// Map just_audio ProcessingState to AudioService AudioProcessingState
  AudioProcessingState _mapProcessingState(ProcessingState state) {
    switch (state) {
      case ProcessingState.idle:
        return AudioProcessingState.idle;
      case ProcessingState.loading:
        return AudioProcessingState.loading;
      case ProcessingState.buffering:
        return AudioProcessingState.buffering;
      case ProcessingState.ready:
        return AudioProcessingState.ready;
      case ProcessingState.completed:
        return AudioProcessingState.completed;
    }
  }

  /// Update queue stream
  void _updateQueue() {
    _queueController.add(List.from(_queue));
  }

  /// Update current index stream
  void _updateCurrentIndex() {
    _currentIndexController.add(_currentIndex);
  }

  /// Dispose resources
  @override
  Future<void> onTaskRemoved() async {
    await stop();
    await _audioPlayer.dispose();
  }

  /// Dispose streams and resources
  Future<void> dispose() async {
    await _queueController.close();
    await _currentIndexController.close();
    await _shuffleController.close();
    await _repeatController.close();
    await _audioPlayer.dispose();
  }
}
