import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// Import services and controllers
import 'services/storage_service.dart';
import 'controllers/audio_controller.dart';
import 'controllers/library_controller.dart';
import 'controllers/download_controller.dart';
import 'controllers/search_controller.dart' as search_ctrl;
import 'controllers/settings_controller.dart';
import 'controllers/equalizer_controller.dart';
import 'models/song.dart';

// Import theme
import 'ui/theme/app_theme.dart';
import 'ui/theme/colors.dart';
import 'ui/theme/glass_container.dart';
import 'ui/widgets/glass_bottom_nav.dart';
import 'ui/screens/library_screen.dart';
import 'ui/screens/search_screen.dart';
import 'ui/screens/settings_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize storage service (includes Hive setup)
  await StorageService.instance.initialize();

  runApp(const VibifyApp());
}

class VibifyApp extends StatelessWidget {
  const VibifyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AudioController()),
        ChangeNotifierProvider(
          create: (_) => LibraryController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => DownloadController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => search_ctrl.SearchController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => SettingsController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => EqualizerController()..initialize(),
        ),
      ],
      child: MaterialApp(
        title: 'Vibify',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const TemporaryHomeScreen(),
      ),
    );
  }
}

// Temporary home screen showcasing glass theme - will be replaced with proper screens
class TemporaryHomeScreen extends StatefulWidget {
  const TemporaryHomeScreen({super.key});

  @override
  State<TemporaryHomeScreen> createState() => _TemporaryHomeScreenState();
}

class _TemporaryHomeScreenState extends State<TemporaryHomeScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDemoSongs();
    });
  }

  void _loadDemoSongs() {
    // Create demo songs for testing - these would normally come from search/import
    final demoSongs = [
      Song(
        id: 'demo_1',
        title: 'Welcome to Vibify',
        artist: 'Vibify Team',
        album: 'Getting Started',
        duration: const Duration(minutes: 3, seconds: 30),
        isFavorite: true,
      ),
      Song(
        id: 'demo_2',
        title: 'Glass UI Demo',
        artist: 'UI Designers',
        album: 'Design Showcase',
        duration: const Duration(minutes: 4, seconds: 15),
        isDownloaded: true,
      ),
      Song(
        id: 'demo_3',
        title: 'Smooth Transitions',
        artist: 'Animation Studio',
        album: 'Motion Graphics',
        duration: const Duration(minutes: 2, seconds: 45),
      ),
    ];

    // Add to controllers for demo
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final audioController = Provider.of<AudioController>(
        context,
        listen: false,
      );
      final libraryController = Provider.of<LibraryController>(
        context,
        listen: false,
      );

      // Add songs to library
      for (final song in demoSongs) {
        libraryController.addSong(song);
      }

      // Play first song
      audioController.playSong(demoSongs.first);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Main content
              Expanded(
                child: IndexedStack(
                  index: _currentIndex,
                  children: [
                    _buildHomeTab(context, theme),
                    const SearchScreen(),
                    const LibraryScreen(),
                    const SettingsScreen(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: GlassBottomNav(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() => _currentIndex = index);
          if (index == 1) {
            // Navigate to search screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SearchScreen()),
            );
          } else if (index == 2) {
            // Navigate to library screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const LibraryScreen()),
            );
          } else if (index == 3) {
            // Navigate to settings screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SettingsScreen()),
            );
          }
        },
        items: const [
          GlassBottomNavItem(
            icon: Icons.home_outlined,
            activeIcon: Icons.home,
            label: 'Home',
          ),
          GlassBottomNavItem(
            icon: Icons.search_outlined,
            activeIcon: Icons.search,
            label: 'Search',
          ),
          GlassBottomNavItem(
            icon: Icons.library_music_outlined,
            activeIcon: Icons.library_music,
            label: 'Library',
          ),
          GlassBottomNavItem(
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildHomeTab(BuildContext context, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: VibifyColors.purpleGradient),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.music_note,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Vibify',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Your Music, Your Vibe',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Welcome section
          GlassContainer.card(
            width: double.infinity,
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: VibifyColors.purpleGradient,
                    ),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.music_note,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Welcome to Vibify',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Experience music with beautiful glass UI',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: GlassContainer.button(
                        onTap: () {
                          setState(() => _currentIndex = 1);
                        },
                        child: const Text(
                          'Explore Music',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Quick actions
          Text(
            'Quick Actions',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: GlassContainer.button(
                  onTap: () {
                    setState(() => _currentIndex = 2);
                  },
                  child: Column(
                    children: [
                      Icon(
                        Icons.library_music,
                        color: VibifyColors.primaryPurple,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Library',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: GlassContainer.button(
                  onTap: () {
                    setState(() => _currentIndex = 3);
                  },
                  child: Column(
                    children: [
                      Icon(
                        Icons.settings,
                        color: VibifyColors.primaryBlue,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Settings',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
