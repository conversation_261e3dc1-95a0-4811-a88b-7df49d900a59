import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// Import services and controllers
import 'services/storage_service.dart';
import 'controllers/audio_controller.dart';
import 'controllers/library_controller.dart';
import 'controllers/download_controller.dart';
import 'controllers/search_controller.dart' as search_ctrl;
import 'controllers/settings_controller.dart';
import 'controllers/equalizer_controller.dart';
import 'models/song.dart';

// Import theme
import 'ui/theme/app_theme.dart';
import 'ui/theme/colors.dart';
import 'ui/theme/glass_container.dart';
import 'ui/widgets/glass_bottom_nav.dart';
import 'ui/widgets/glass_music_player.dart';
import 'ui/widgets/glass_download_widgets.dart';
import 'ui/screens/downloads_screen.dart';
import 'ui/screens/library_screen.dart';
import 'ui/screens/search_screen.dart';
import 'ui/screens/settings_screen.dart';
import 'ui/screens/splash_screen.dart';

// Import screens (will be created later)
// import 'ui/screens/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize storage service (includes Hive setup)
  await StorageService.instance.initialize();

  runApp(const VibifyApp());
}

class VibifyApp extends StatelessWidget {
  const VibifyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AudioController()),
        ChangeNotifierProvider(
          create: (_) => LibraryController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => DownloadController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => search_ctrl.SearchController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => SettingsController()..initialize(),
        ),
        ChangeNotifierProvider(
          create: (_) => EqualizerController()..initialize(),
        ),
      ],
      child: MaterialApp(
        title: 'Vibify',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const TemporaryHomeScreen(),
      ),
    );
  }
}

// Temporary home screen showcasing glass theme - will be replaced with proper screens
class TemporaryHomeScreen extends StatefulWidget {
  const TemporaryHomeScreen({super.key});

  @override
  State<TemporaryHomeScreen> createState() => _TemporaryHomeScreenState();
}

class _TemporaryHomeScreenState extends State<TemporaryHomeScreen> {
  int _currentIndex = 0;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialization will be handled by splash screen
  }

  void _loadDemoSong() {
    // Create demo songs for testing
    final demoSongs = [
      Song(
        id: 'demo_1',
        title: 'Demo Song',
        artist: 'Vibify Demo',
        album: 'Demo Album',
        duration: const Duration(minutes: 3, seconds: 30),
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        isFavorite: true,
      ),
      Song(
        id: 'demo_2',
        title: 'Glass Dreams',
        artist: 'Crystal Sounds',
        album: 'Transparent Melodies',
        duration: const Duration(minutes: 4, seconds: 15),
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        isDownloaded: true,
      ),
      Song(
        id: 'demo_3',
        title: 'Liquid Harmony',
        artist: 'Flow Artists',
        album: 'Fluid Beats',
        duration: const Duration(minutes: 2, seconds: 45),
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      ),
      Song(
        id: 'demo_4',
        title: 'Vibify Anthem',
        artist: 'Vibify Demo',
        album: 'Demo Album',
        duration: const Duration(minutes: 3, seconds: 50),
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        isFavorite: true,
        isDownloaded: true,
      ),
    ];

    // Add to controllers for demo
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final audioController = Provider.of<AudioController>(
        context,
        listen: false,
      );
      final libraryController = Provider.of<LibraryController>(
        context,
        listen: false,
      );

      // Add songs to library
      for (final song in demoSongs) {
        libraryController.addSong(song);
      }

      // Play first song
      audioController.playSong(demoSongs.first);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show splash screen during initialization
    if (!_isInitialized) {
      return SplashScreen(
        onInitializationComplete: () {
          setState(() {
            _isInitialized = true;
          });
          _loadDemoSong();
        },
      );
    }

    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Glass App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    GlassContainer.card(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.music_note,
                        color: VibifyColors.primaryPurple,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Vibify',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    GlassContainer.button(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.search,
                        color: VibifyColors.getTextColor(brightness),
                      ),
                    ),
                  ],
                ),
              ),

              // Main content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Welcome card
                      GlassContainer.card(
                        width: double.infinity,
                        child: Column(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: VibifyColors.purpleGradient,
                                ),
                                borderRadius: BorderRadius.circular(40),
                                boxShadow: [
                                  BoxShadow(
                                    color: VibifyColors.primaryPurple
                                        .withValues(alpha: 0.3),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.music_note,
                                size: 40,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 20),
                            Text(
                              'Welcome to Vibify',
                              style: theme.textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your music streaming app with Liquid Glass UI',
                              textAlign: TextAlign.center,
                              style: theme.textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 20),
                            Row(
                              children: [
                                Expanded(
                                  child: GlassContainer.button(
                                    child: const Text(
                                      'Get Started',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Consumer<DownloadController>(
                                  builder: (context, downloadController, child) {
                                    return GlassDownloadButton(
                                      song: Song(
                                        id: 'demo_1',
                                        title: 'Demo Song',
                                        artist: 'Vibify Demo',
                                        album: 'Demo Album',
                                        duration: const Duration(
                                          minutes: 3,
                                          seconds: 30,
                                        ),
                                        youtubeUrl:
                                            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                      ),
                                      size: 48,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Feature cards
                      Row(
                        children: [
                          Expanded(
                            child: GlassContainer.card(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.download,
                                    color: VibifyColors.primaryBlue,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Downloads',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: GlassContainer.card(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.favorite,
                                    color: VibifyColors.primaryPink,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Favorites',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomSheet: const GlassMiniPlayer(),
      bottomNavigationBar: GlassBottomNav(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() => _currentIndex = index);
          if (index == 1) {
            // Navigate to search screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SearchScreen()),
            );
          } else if (index == 2) {
            // Navigate to library screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const LibraryScreen()),
            );
          } else if (index == 3) {
            // Navigate to settings screen
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SettingsScreen()),
            );
          }
        },
        items: const [
          GlassBottomNavItem(
            icon: Icons.home_outlined,
            activeIcon: Icons.home,
            label: 'Home',
          ),
          GlassBottomNavItem(
            icon: Icons.search_outlined,
            activeIcon: Icons.search,
            label: 'Search',
          ),
          GlassBottomNavItem(
            icon: Icons.library_music_outlined,
            activeIcon: Icons.library_music,
            label: 'Library',
          ),
          GlassBottomNavItem(
            icon: Icons.settings_outlined,
            activeIcon: Icons.settings,
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}
