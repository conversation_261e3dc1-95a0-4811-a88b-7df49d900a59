import 'package:flutter/material.dart';

/// Vibify color palette for liquid glass theme
class VibifyColors {
  VibifyColors._();

  // Primary brand colors
  static const Color primaryPurple = Color(0xFF6366F1);
  static const Color primaryBlue = Color(0xFF3B82F6);
  static const Color primaryPink = Color(0xFFEC4899);
  static const Color primaryCyan = Color(0xFF06B6D4);

  // Glass effect colors
  static const Color glassLight = Color(0x1AFFFFFF);
  static const Color glassMedium = Color(0x33FFFFFF);
  static const Color glassDark = Color(0x0D000000);
  static const Color glassBlur = Color(0x80FFFFFF);

  // Background gradients
  static const List<Color> lightGradient = [
    Color(0xFFF8FAFC),
    Color(0xFFE2E8F0),
    Color(0xFFCBD5E1),
  ];

  static const List<Color> darkGradient = [
    Color(0xFF0F172A),
    Color(0xFF1E293B),
    Color(0xFF334155),
  ];

  // Accent gradients for glass elements
  static const List<Color> purpleGradient = [
    Color(0xFF8B5CF6),
    Color(0xFF6366F1),
    Color(0xFF3B82F6),
  ];

  static const List<Color> pinkGradient = [
    Color(0xFFEC4899),
    Color(0xFFEF4444),
    Color(0xFFF97316),
  ];

  static const List<Color> cyanGradient = [
    Color(0xFF06B6D4),
    Color(0xFF0EA5E9),
    Color(0xFF3B82F6),
  ];

  // Text colors
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color textOnGlass = Color(0xFFFFFFFF);
  static const Color textOnGlassDark = Color(0xE6FFFFFF);

  // Dark theme text colors
  static const Color textPrimaryDark = Color(0xFFF9FAFB);
  static const Color textSecondaryDark = Color(0xFFD1D5DB);
  static const Color textTertiaryDark = Color(0xFF9CA3AF);

  // Status colors
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // Glass overlay colors
  static const Color glassOverlayLight = Color(0x1A000000);
  static const Color glassOverlayDark = Color(0x33000000);
  static const Color glassOverlayMedium = Color(0x26000000);

  // Shimmer colors for loading states
  static const Color shimmerBase = Color(0xFFE5E7EB);
  static const Color shimmerHighlight = Color(0xFFF3F4F6);
  static const Color shimmerBaseDark = Color(0xFF374151);
  static const Color shimmerHighlightDark = Color(0xFF4B5563);

  // Music player specific colors
  static const Color playerBackground = Color(0x0DFFFFFF);
  static const Color playerBackgroundDark = Color(0x1A000000);
  static const Color seekBarActive = Color(0xFF6366F1);
  static const Color seekBarInactive = Color(0x33FFFFFF);
  static const Color volumeSlider = Color(0xFF8B5CF6);

  // Download status colors
  static const Color downloadPending = Color(0xFFF59E0B);
  static const Color downloadActive = Color(0xFF3B82F6);
  static const Color downloadComplete = Color(0xFF10B981);
  static const Color downloadError = Color(0xFFEF4444);
  static const Color downloadPaused = Color(0xFF6B7280);

  // Helper methods for creating gradients
  static LinearGradient createGradient(List<Color> colors, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
    );
  }

  static RadialGradient createRadialGradient(List<Color> colors, {
    AlignmentGeometry center = Alignment.center,
    double radius = 0.5,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
    );
  }

  // Glass effect gradients
  static LinearGradient get glassGradientLight => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      glassLight,
      glassMedium.withOpacity(0.1),
      glassLight.withOpacity(0.05),
    ],
  );

  static LinearGradient get glassGradientDark => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      glassDark,
      glassDark.withOpacity(0.3),
      glassDark.withOpacity(0.1),
    ],
  );

  // Border gradients for glass elements
  static LinearGradient get glassBorderLight => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Colors.white.withOpacity(0.3),
      Colors.white.withOpacity(0.1),
      Colors.white.withOpacity(0.05),
    ],
  );

  static LinearGradient get glassBorderDark => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Colors.white.withOpacity(0.1),
      Colors.white.withOpacity(0.05),
      Colors.white.withOpacity(0.02),
    ],
  );

  // Dynamic color based on brightness
  static Color getTextColor(Brightness brightness, {bool isPrimary = true}) {
    if (brightness == Brightness.dark) {
      return isPrimary ? textPrimaryDark : textSecondaryDark;
    } else {
      return isPrimary ? textPrimary : textSecondary;
    }
  }

  static Color getGlassColor(Brightness brightness) {
    return brightness == Brightness.dark ? glassDark : glassLight;
  }

  static LinearGradient getGlassGradient(Brightness brightness) {
    return brightness == Brightness.dark ? glassGradientDark : glassGradientLight;
  }

  static LinearGradient getGlassBorder(Brightness brightness) {
    return brightness == Brightness.dark ? glassBorderDark : glassBorderLight;
  }

  // Color utilities
  static Color withGlassOpacity(Color color, {double opacity = 0.1}) {
    return color.withOpacity(opacity);
  }

  static Color blendWithGlass(Color color, Brightness brightness) {
    final glassColor = getGlassColor(brightness);
    return Color.alphaBlend(glassColor, color);
  }
}
