import 'dart:ui';
import 'package:flutter/material.dart';
import 'colors.dart';

/// A reusable glass container widget that creates glassmorphism effects
class GlassContainer extends StatelessWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final double blur;
  final double opacity;
  final Color? color;
  final Gradient? gradient;
  final Border? border;
  final List<BoxShadow>? boxShadow;
  final VoidCallback? onTap;
  final bool enableBorder;
  final double borderWidth;
  final GlassStyle style;

  const GlassContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius,
    this.blur = 10.0,
    this.opacity = 0.1,
    this.color,
    this.gradient,
    this.border,
    this.boxShadow,
    this.onTap,
    this.enableBorder = true,
    this.borderWidth = 1.0,
    this.style = GlassStyle.normal,
  });

  /// Creates a glass card with default styling
  const GlassContainer.card({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(8.0),
    this.onTap,
  }) : borderRadius = const BorderRadius.all(Radius.circular(16.0)),
       blur = 15.0,
       opacity = 0.15,
       color = null,
       gradient = null,
       border = null,
       boxShadow = null,
       enableBorder = true,
       borderWidth = 1.0,
       style = GlassStyle.card;

  /// Creates a glass button with interactive styling
  const GlassContainer.button({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
    this.margin,
    this.onTap,
  }) : borderRadius = const BorderRadius.all(Radius.circular(12.0)),
       blur = 12.0,
       opacity = 0.2,
       color = null,
       gradient = null,
       border = null,
       boxShadow = null,
       enableBorder = true,
       borderWidth = 1.5,
       style = GlassStyle.button;

  /// Creates a glass dialog with enhanced blur
  const GlassContainer.dialog({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(24.0),
    this.margin,
    this.onTap,
  }) : borderRadius = const BorderRadius.all(Radius.circular(20.0)),
       blur = 20.0,
       opacity = 0.25,
       color = null,
       gradient = null,
       border = null,
       boxShadow = null,
       enableBorder = true,
       borderWidth = 1.0,
       style = GlassStyle.dialog;

  /// Creates a glass navigation bar
  const GlassContainer.navbar({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
    this.margin,
    this.onTap,
  }) : borderRadius = const BorderRadius.vertical(top: Radius.circular(20.0)),
       blur = 25.0,
       opacity = 0.3,
       color = null,
       gradient = null,
       border = null,
       boxShadow = null,
       enableBorder = true,
       borderWidth = 1.0,
       style = GlassStyle.navbar;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;
    
    // Determine colors based on theme and style
    final effectiveColor = _getEffectiveColor(brightness);
    final effectiveGradient = _getEffectiveGradient(brightness);
    final effectiveBorder = _getEffectiveBorder(brightness);
    final effectiveShadow = _getEffectiveShadow(brightness);

    Widget container = Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(12.0),
        border: enableBorder ? effectiveBorder : border,
        boxShadow: boxShadow ?? effectiveShadow,
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(12.0),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            decoration: BoxDecoration(
              color: effectiveColor,
              gradient: effectiveGradient,
              borderRadius: borderRadius ?? BorderRadius.circular(12.0),
            ),
            child: child,
          ),
        ),
      ),
    );

    // Add tap functionality if provided
    if (onTap != null) {
      container = GestureDetector(
        onTap: onTap,
        child: container,
      );
    }

    return container;
  }

  Color _getEffectiveColor(Brightness brightness) {
    if (color != null) return color!;
    
    switch (style) {
      case GlassStyle.normal:
      case GlassStyle.card:
        return VibifyColors.getGlassColor(brightness).withOpacity(opacity);
      case GlassStyle.button:
        return brightness == Brightness.dark
            ? VibifyColors.glassMedium.withOpacity(opacity)
            : VibifyColors.glassLight.withOpacity(opacity * 1.5);
      case GlassStyle.dialog:
        return brightness == Brightness.dark
            ? VibifyColors.glassDark.withOpacity(opacity * 1.2)
            : VibifyColors.glassLight.withOpacity(opacity * 1.3);
      case GlassStyle.navbar:
        return brightness == Brightness.dark
            ? VibifyColors.glassDark.withOpacity(opacity * 1.5)
            : VibifyColors.glassLight.withOpacity(opacity * 1.8);
    }
  }

  Gradient? _getEffectiveGradient(Brightness brightness) {
    if (gradient != null) return gradient;
    
    switch (style) {
      case GlassStyle.normal:
      case GlassStyle.card:
        return null;
      case GlassStyle.button:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VibifyColors.getGlassColor(brightness).withOpacity(opacity * 1.2),
            VibifyColors.getGlassColor(brightness).withOpacity(opacity * 0.8),
          ],
        );
      case GlassStyle.dialog:
      case GlassStyle.navbar:
        return VibifyColors.getGlassGradient(brightness);
    }
  }

  Border _getEffectiveBorder(Brightness brightness) {
    if (border != null) return border!;
    
    final borderColor = brightness == Brightness.dark
        ? Colors.white.withOpacity(0.1)
        : Colors.white.withOpacity(0.3);
    
    return Border.all(
      color: borderColor,
      width: borderWidth,
    );
  }

  List<BoxShadow> _getEffectiveShadow(Brightness brightness) {
    switch (style) {
      case GlassStyle.normal:
      case GlassStyle.card:
        return [
          BoxShadow(
            color: brightness == Brightness.dark
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: 10.0,
            offset: const Offset(0, 4),
          ),
        ];
      case GlassStyle.button:
        return [
          BoxShadow(
            color: brightness == Brightness.dark
                ? Colors.black.withOpacity(0.4)
                : Colors.black.withOpacity(0.15),
            blurRadius: 8.0,
            offset: const Offset(0, 2),
          ),
        ];
      case GlassStyle.dialog:
        return [
          BoxShadow(
            color: brightness == Brightness.dark
                ? Colors.black.withOpacity(0.5)
                : Colors.black.withOpacity(0.2),
            blurRadius: 20.0,
            offset: const Offset(0, 8),
          ),
        ];
      case GlassStyle.navbar:
        return [
          BoxShadow(
            color: brightness == Brightness.dark
                ? Colors.black.withOpacity(0.6)
                : Colors.black.withOpacity(0.1),
            blurRadius: 15.0,
            offset: const Offset(0, -2),
          ),
        ];
    }
  }
}

/// Different styles for glass containers
enum GlassStyle {
  normal,
  card,
  button,
  dialog,
  navbar,
}

/// Extension for easy glass styling
extension GlassContainerExtension on Widget {
  Widget glassify({
    double blur = 10.0,
    double opacity = 0.1,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return GlassContainer(
      blur: blur,
      opacity: opacity,
      borderRadius: borderRadius,
      padding: padding,
      margin: margin,
      onTap: onTap,
      child: this,
    );
  }
}
