import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'colors.dart';

/// Main theme configuration for Vibify app
class AppTheme {
  AppTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // Color scheme
      colorScheme: ColorScheme.fromSeed(
        seedColor: VibifyColors.primaryPurple,
        brightness: Brightness.light,
        primary: VibifyColors.primaryPurple,
        secondary: VibifyColors.primaryBlue,
        tertiary: VibifyColors.primaryPink,
        surface: VibifyColors.lightGradient[0],
        background: VibifyColors.lightGradient[0],
        error: VibifyColors.error,
      ),

      // App bar theme
      appBarTheme: AppBarTheme(
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: VibifyColors.textPrimary,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: const TextStyle(
          color: VibifyColors.textPrimary,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: const IconThemeData(color: VibifyColors.textPrimary),
      ),

      // Card theme
      cardTheme: CardThemeData(
        elevation: 0,
        color: VibifyColors.glassLight,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: VibifyColors.primaryPurple,
        unselectedItemColor: VibifyColors.textSecondary,
        type: BottomNavigationBarType.fixed,
      ),

      // Floating action button theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: VibifyColors.primaryPurple,
        foregroundColor: Colors.white,
        elevation: 8,
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: VibifyColors.primaryPurple,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: VibifyColors.primaryPurple,
          side: const BorderSide(color: VibifyColors.primaryPurple),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: VibifyColors.primaryPurple,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: VibifyColors.glassLight,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: VibifyColors.primaryPurple,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // Slider theme
      sliderTheme: SliderThemeData(
        activeTrackColor: VibifyColors.primaryPurple,
        inactiveTrackColor: VibifyColors.textTertiary.withOpacity(0.3),
        thumbColor: VibifyColors.primaryPurple,
        overlayColor: VibifyColors.primaryPurple.withOpacity(0.2),
        trackHeight: 4,
      ),

      // Progress indicator theme
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: VibifyColors.primaryPurple,
        linearTrackColor: VibifyColors.textTertiary,
      ),

      // Icon theme
      iconTheme: const IconThemeData(
        color: VibifyColors.textSecondary,
        size: 24,
      ),

      // Text theme
      textTheme: _buildTextTheme(Brightness.light),

      // Divider theme
      dividerTheme: DividerThemeData(
        color: VibifyColors.textTertiary.withOpacity(0.2),
        thickness: 1,
      ),

      // List tile theme
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        iconColor: VibifyColors.textSecondary,
        textColor: VibifyColors.textPrimary,
      ),

      // Dialog theme
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),

      // Bottom sheet theme
      bottomSheetTheme: const BottomSheetThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
      ),
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,

      // Color scheme
      colorScheme: ColorScheme.fromSeed(
        seedColor: VibifyColors.primaryPurple,
        brightness: Brightness.dark,
        primary: VibifyColors.primaryPurple,
        secondary: VibifyColors.primaryBlue,
        tertiary: VibifyColors.primaryPink,
        surface: VibifyColors.darkGradient[0],
        background: VibifyColors.darkGradient[0],
        error: VibifyColors.error,
      ),

      // App bar theme
      appBarTheme: AppBarTheme(
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: VibifyColors.textPrimaryDark,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: const TextStyle(
          color: VibifyColors.textPrimaryDark,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: const IconThemeData(color: VibifyColors.textPrimaryDark),
      ),

      // Card theme
      cardTheme: CardThemeData(
        elevation: 0,
        color: VibifyColors.glassDark,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: VibifyColors.primaryPurple,
        unselectedItemColor: VibifyColors.textSecondaryDark,
        type: BottomNavigationBarType.fixed,
      ),

      // Button themes (similar to light theme)
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: VibifyColors.primaryPurple,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: VibifyColors.glassDark,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: VibifyColors.primaryPurple,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // Text theme
      textTheme: _buildTextTheme(Brightness.dark),

      // Icon theme
      iconTheme: const IconThemeData(
        color: VibifyColors.textSecondaryDark,
        size: 24,
      ),

      // Other themes similar to light theme but with dark colors
      sliderTheme: SliderThemeData(
        activeTrackColor: VibifyColors.primaryPurple,
        inactiveTrackColor: VibifyColors.textTertiaryDark.withOpacity(0.3),
        thumbColor: VibifyColors.primaryPurple,
        overlayColor: VibifyColors.primaryPurple.withOpacity(0.2),
        trackHeight: 4,
      ),

      dividerTheme: DividerThemeData(
        color: VibifyColors.textTertiaryDark.withOpacity(0.2),
        thickness: 1,
      ),

      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        iconColor: VibifyColors.textSecondaryDark,
        textColor: VibifyColors.textPrimaryDark,
      ),
    );
  }

  /// Build text theme for the given brightness
  static TextTheme _buildTextTheme(Brightness brightness) {
    final primaryColor = brightness == Brightness.dark
        ? VibifyColors.textPrimaryDark
        : VibifyColors.textPrimary;
    final secondaryColor = brightness == Brightness.dark
        ? VibifyColors.textSecondaryDark
        : VibifyColors.textSecondary;

    return TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: primaryColor,
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: primaryColor,
      ),
      displaySmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: primaryColor,
      ),
      headlineLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: primaryColor,
      ),
      headlineMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: primaryColor,
      ),
      headlineSmall: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w500,
        color: primaryColor,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryColor,
      ),
      titleMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryColor,
      ),
      titleSmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: secondaryColor,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: primaryColor,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: secondaryColor,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        color: secondaryColor,
      ),
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryColor,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: secondaryColor,
      ),
      labelSmall: TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w500,
        color: secondaryColor,
      ),
    );
  }
}
