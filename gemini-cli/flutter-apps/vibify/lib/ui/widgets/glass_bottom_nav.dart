import 'dart:ui';
import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';

/// Glass-themed bottom navigation bar
class GlassBottomNav extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final List<GlassBottomNavItem> items;
  final double height;
  final bool showLabels;
  final double blurIntensity;

  const GlassBottomNav({
    super.key,
    required this.currentIndex,
    required this.items,
    this.onTap,
    this.height = 80,
    this.showLabels = true,
    this.blurIntensity = 25.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;
    final mediaQuery = MediaQuery.of(context);

    return Container(
      height: height + mediaQuery.padding.bottom,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: brightness == Brightness.dark
              ? [
                  VibifyColors.darkGradient[0].withOpacity(0.7),
                  VibifyColors.darkGradient[0].withOpacity(0.9),
                ]
              : [
                  VibifyColors.lightGradient[0].withOpacity(0.7),
                  VibifyColors.lightGradient[0].withOpacity(0.9),
                ],
        ),
      ),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: blurIntensity,
            sigmaY: blurIntensity,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: brightness == Brightness.dark
                  ? VibifyColors.glassDark.withOpacity(0.4)
                  : VibifyColors.glassLight.withOpacity(0.4),
              border: Border(
                top: BorderSide(
                  color: brightness == Brightness.dark
                      ? Colors.white.withOpacity(0.1)
                      : Colors.white.withOpacity(0.3),
                  width: 0.5,
                ),
              ),
            ),
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: items.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    final isSelected = index == currentIndex;

                    return _buildNavItem(
                      context,
                      item,
                      isSelected,
                      () => onTap?.call(index),
                      brightness,
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    GlassBottomNavItem item,
    bool isSelected,
    VoidCallback? onTap,
    Brightness brightness,
  ) {
    final primaryColor = VibifyColors.getTextColor(brightness);
    final secondaryColor = VibifyColors.getTextColor(
      brightness,
      isPrimary: false,
    );

    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon with selection indicator
              Stack(
                alignment: Alignment.center,
                children: [
                  // Selection background
                  if (isSelected)
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: VibifyColors.purpleGradient,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: VibifyColors.primaryPurple.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),

                  // Icon
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      isSelected ? item.activeIcon : item.icon,
                      color: isSelected ? Colors.white : secondaryColor,
                      size: 24,
                    ),
                  ),

                  // Badge
                  if (item.showBadge)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: VibifyColors.error,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: item.badgeText != null
                            ? Text(
                                item.badgeText!,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              )
                            : null,
                      ),
                    ),
                ],
              ),

              // Label
              if (showLabels) ...[
                const SizedBox(height: 4),
                AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 200),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? VibifyColors.primaryPurple
                        : secondaryColor,
                  ),
                  child: Text(
                    item.label,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Navigation item for glass bottom nav
class GlassBottomNavItem {
  final IconData icon;
  final IconData? activeIcon;
  final String label;
  final bool showBadge;
  final String? badgeText;

  const GlassBottomNavItem({
    required this.icon,
    required this.label,
    this.activeIcon,
    this.showBadge = false,
    this.badgeText,
  });
}

/// Floating glass bottom navigation bar
class FloatingGlassBottomNav extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final List<GlassBottomNavItem> items;
  final double height;
  final bool showLabels;
  final EdgeInsets margin;
  final double borderRadius;

  const FloatingGlassBottomNav({
    super.key,
    required this.currentIndex,
    required this.items,
    this.onTap,
    this.height = 70,
    this.showLabels = false,
    this.margin = const EdgeInsets.all(16),
    this.borderRadius = 25,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Container(
      margin: margin,
      child: GlassContainer(
        height: height,
        borderRadius: BorderRadius.circular(borderRadius),
        style: GlassStyle.navbar,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = index == currentIndex;

            return _buildFloatingNavItem(
              context,
              item,
              isSelected,
              () => onTap?.call(index),
              brightness,
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFloatingNavItem(
    BuildContext context,
    GlassBottomNavItem item,
    bool isSelected,
    VoidCallback? onTap,
    Brightness brightness,
  ) {
    final secondaryColor = VibifyColors.getTextColor(
      brightness,
      isPrimary: false,
    );

    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon with selection indicator
              Stack(
                alignment: Alignment.center,
                children: [
                  // Selection background
                  if (isSelected)
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: VibifyColors.purpleGradient,
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: VibifyColors.primaryPurple.withValues(
                              alpha: 0.4,
                            ),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                    ),

                  // Icon
                  Icon(
                    isSelected ? item.activeIcon ?? item.icon : item.icon,
                    color: isSelected ? Colors.white : secondaryColor,
                    size: 22,
                  ),

                  // Badge
                  if (item.showBadge)
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: VibifyColors.error,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: item.badgeText != null
                            ? Text(
                                item.badgeText!,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              )
                            : null,
                      ),
                    ),
                ],
              ),

              // Label (if enabled)
              if (showLabels) ...[
                const SizedBox(height: 4),
                Text(
                  item.label,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? VibifyColors.primaryPurple
                        : secondaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
