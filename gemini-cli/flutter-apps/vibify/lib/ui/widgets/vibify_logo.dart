import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';

/// Vibify logo widget for use throughout the app
class VibifyLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final bool animated;

  const VibifyLogo({
    super.key,
    this.size = 120,
    this.showText = true,
    this.animated = false,
  });

  @override
  Widget build(BuildContext context) {
    if (animated) {
      return _AnimatedLogo(
        size: size,
        showText: showText,
      );
    }

    return _StaticLogo(
      size: size,
      showText: showText,
    );
  }
}

class _StaticLogo extends StatelessWidget {
  final double size;
  final bool showText;

  const _StaticLogo({
    required this.size,
    required this.showText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLogoIcon(),
        if (showText) ...[
          SizedBox(height: size * 0.2),
          _buildLogoText(),
        ],
      ],
    );
  }

  Widget _buildLogoIcon() {
    return GlassContainer(
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(size * 0.25),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size * 0.25),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: VibifyColors.purpleGradient,
          ),
        ),
        child: Stack(
          children: [
            // Glass highlight
            Positioned(
              top: size * 0.1,
              left: size * 0.1,
              child: Container(
                width: size * 0.4,
                height: size * 0.6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(size * 0.2),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.3),
                      Colors.white.withValues(alpha: 0.1),
                    ],
                  ),
                ),
              ),
            ),
            
            // Music note icon
            Center(
              child: _buildMusicNote(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMusicNote() {
    final noteSize = size * 0.5;
    
    return SizedBox(
      width: noteSize,
      height: noteSize,
      child: CustomPaint(
        painter: MusicNotePainter(
          color: Colors.white,
          size: noteSize,
        ),
      ),
    );
  }

  Widget _buildLogoText() {
    return Column(
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: VibifyColors.purpleGradient,
          ).createShader(bounds),
          child: Text(
            'Vibify',
            style: TextStyle(
              fontSize: size * 0.4,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        SizedBox(height: size * 0.05),
        Text(
          'Your Music, Your Vibe',
          style: TextStyle(
            fontSize: size * 0.15,
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }
}

class _AnimatedLogo extends StatefulWidget {
  final double size;
  final bool showText;

  const _AnimatedLogo({
    required this.size,
    required this.showText,
  });

  @override
  State<_AnimatedLogo> createState() => _AnimatedLogoState();
}

class _AnimatedLogoState extends State<_AnimatedLogo>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _scaleController.forward();
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _rotationAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 0.1, // Subtle rotation
            child: _StaticLogo(
              size: widget.size,
              showText: widget.showText,
            ),
          ),
        );
      },
    );
  }
}

/// Custom painter for the music note icon
class MusicNotePainter extends CustomPainter {
  final Color color;
  final double size;

  MusicNotePainter({
    required this.color,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final center = Offset(canvasSize.width / 2, canvasSize.height / 2);
    final noteWidth = size * 0.08;
    final noteHeight = size * 0.7;

    // Note stem
    final stemRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(center.dx + size * 0.15, center.dy - size * 0.1),
        width: noteWidth,
        height: noteHeight,
      ),
      Radius.circular(noteWidth / 2),
    );
    canvas.drawRRect(stemRect, paint);

    // Note head
    final headCenter = Offset(center.dx + size * 0.15, center.dy + size * 0.2);
    canvas.drawOval(
      Rect.fromCenter(
        center: headCenter,
        width: size * 0.2,
        height: size * 0.14,
      ),
      paint,
    );

    // Musical staff lines (decorative)
    final linePaint = Paint()
      ..color = color.withValues(alpha: 0.6)
      ..strokeWidth = size * 0.02
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < 5; i++) {
      final y = center.dy - size * 0.2 + (i * size * 0.1);
      canvas.drawLine(
        Offset(center.dx - size * 0.3, y),
        Offset(center.dx + size * 0.3, y),
        linePaint,
      );
    }

    // Sound waves
    final wavePaint = Paint()
      ..color = color.withValues(alpha: 0.7)
      ..strokeWidth = size * 0.03
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final wavePath1 = Path();
    wavePath1.moveTo(center.dx + size * 0.35, center.dy - size * 0.1);
    wavePath1.quadraticBezierTo(
      center.dx + size * 0.45,
      center.dy - size * 0.05,
      center.dx + size * 0.35,
      center.dy,
    );
    wavePath1.quadraticBezierTo(
      center.dx + size * 0.45,
      center.dy + size * 0.05,
      center.dx + size * 0.35,
      center.dy + size * 0.1,
    );
    canvas.drawPath(wavePath1, wavePaint);

    final wavePath2 = Path();
    wavePath2.moveTo(center.dx + size * 0.4, center.dy - size * 0.15);
    wavePath2.quadraticBezierTo(
      center.dx + size * 0.55,
      center.dx + size * 0.075,
      center.dx + size * 0.4,
      center.dy,
    );
    wavePath2.quadraticBezierTo(
      center.dx + size * 0.55,
      center.dy + size * 0.075,
      center.dx + size * 0.4,
      center.dy + size * 0.15,
    );
    
    final wave2Paint = Paint()
      ..color = color.withValues(alpha: 0.5)
      ..strokeWidth = size * 0.03
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    canvas.drawPath(wavePath2, wave2Paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
