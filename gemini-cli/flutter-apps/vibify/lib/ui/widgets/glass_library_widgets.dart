import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_download_widgets.dart';
import '../../models/song.dart';
import '../../models/playlist.dart';
import '../../controllers/audio_controller.dart';
import '../../controllers/download_controller.dart';

/// Glass-themed song tile for library lists
class GlassSongTile extends StatelessWidget {
  final Song song;
  final VoidCallback? onTap;
  final VoidCallback? onMoreTap;
  final bool showDownloadButton;
  final bool showIndex;
  final int? index;
  final bool isPlaying;

  const GlassSongTile({
    super.key,
    required this.song,
    this.onTap,
    this.onMoreTap,
    this.showDownloadButton = true,
    this.showIndex = false,
    this.index,
    this.isPlaying = false,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer.card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      onTap: onTap,
      child: Row(
        children: [
          // Index or album art
          _buildLeading(),
          const SizedBox(width: 16),
          
          // Song info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  song.title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: isPlaying ? VibifyColors.primaryPurple : null,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  song.artist,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (song.album != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    song.album!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          
          // Duration
          if (song.duration != null) ...[
            Text(
              song.formattedDuration,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(width: 12),
          ],
          
          // Download button
          if (showDownloadButton)
            GlassDownloadButton(
              song: song,
              size: 32,
            ),
          
          // More button
          if (onMoreTap != null) ...[
            const SizedBox(width: 8),
            GlassContainer.button(
              padding: const EdgeInsets.all(8),
              onTap: onMoreTap,
              child: Icon(
                Icons.more_vert,
                size: 20,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLeading() {
    if (showIndex && index != null) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isPlaying 
              ? VibifyColors.primaryPurple.withValues(alpha: 0.2)
              : VibifyColors.glassLight.withValues(alpha: 0.3),
        ),
        child: Center(
          child: isPlaying
              ? Icon(
                  Icons.equalizer,
                  color: VibifyColors.primaryPurple,
                  size: 24,
                )
              : Text(
                  '${index! + 1}',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
        ),
      );
    }

    // Album art or default icon
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: song.primaryImageUrl != null 
            ? null 
            : LinearGradient(colors: VibifyColors.purpleGradient),
      ),
      child: song.primaryImageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                song.primaryImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultArt(),
              ),
            )
          : _buildDefaultArt(),
    );
  }

  Widget _buildDefaultArt() {
    return const Icon(
      Icons.music_note,
      color: Colors.white,
      size: 24,
    );
  }
}

/// Glass-themed playlist tile
class GlassPlaylistTile extends StatelessWidget {
  final Playlist playlist;
  final int songCount;
  final VoidCallback? onTap;
  final VoidCallback? onMoreTap;
  final String? subtitle;

  const GlassPlaylistTile({
    super.key,
    required this.playlist,
    required this.songCount,
    this.onTap,
    this.onMoreTap,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer.card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      onTap: onTap,
      child: Row(
        children: [
          // Playlist icon
          _buildPlaylistIcon(),
          const SizedBox(width: 16),
          
          // Playlist info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  playlist.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle ?? '$songCount songs',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (playlist.description != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    playlist.description!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          
          // More button
          if (onMoreTap != null)
            GlassContainer.button(
              padding: const EdgeInsets.all(8),
              onTap: onMoreTap,
              child: Icon(
                Icons.more_vert,
                size: 20,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPlaylistIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          colors: playlist.isSystemPlaylist 
              ? VibifyColors.cyanGradient
              : VibifyColors.purpleGradient,
        ),
      ),
      child: Icon(
        playlist.isSystemPlaylist ? _getSystemPlaylistIcon() : Icons.queue_music,
        color: Colors.white,
        size: 24,
      ),
    );
  }

  IconData _getSystemPlaylistIcon() {
    switch (playlist.id) {
      case 'favorites':
        return Icons.favorite;
      case 'downloads':
        return Icons.download_done;
      case 'recent':
        return Icons.access_time;
      default:
        return Icons.queue_music;
    }
  }
}

/// Glass-themed library section header
class GlassLibraryHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onSeeAllTap;
  final Widget? trailing;

  const GlassLibraryHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.onSeeAllTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          if (trailing != null)
            trailing!
          else if (onSeeAllTap != null)
            GlassContainer.button(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              onTap: onSeeAllTap,
              child: const Text(
                'See All',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Glass-themed search bar for library
class GlassLibrarySearchBar extends StatefulWidget {
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onClear;
  final TextEditingController? controller;

  const GlassLibrarySearchBar({
    super.key,
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.controller,
  });

  @override
  State<GlassLibrarySearchBar> createState() => _GlassLibrarySearchBarState();
}

class _GlassLibrarySearchBarState extends State<GlassLibrarySearchBar> {
  late TextEditingController _controller;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
    _hasText = _controller.text.isNotEmpty;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
    widget.onChanged?.call(_controller.text);
  }

  void _onClear() {
    _controller.clear();
    widget.onClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      borderRadius: BorderRadius.circular(25),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _controller,
              onSubmitted: widget.onSubmitted,
              style: const TextStyle(fontSize: 16),
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Search your library...',
                hintStyle: TextStyle(color: Colors.grey[600]),
                border: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          if (_hasText)
            GestureDetector(
              onTap: _onClear,
              child: Icon(
                Icons.clear,
                color: Colors.grey[600],
                size: 20,
              ),
            ),
        ],
      ),
    );
  }
}

/// Glass-themed filter chips for library
class GlassFilterChips extends StatelessWidget {
  final List<String> filters;
  final String selectedFilter;
  final ValueChanged<String> onFilterChanged;

  const GlassFilterChips({
    super.key,
    required this.filters,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = filter == selectedFilter;
          
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: GlassContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              borderRadius: BorderRadius.circular(20),
              color: isSelected 
                  ? VibifyColors.primaryPurple.withValues(alpha: 0.3)
                  : null,
              onTap: () => onFilterChanged(filter),
              child: Text(
                filter,
                style: TextStyle(
                  color: isSelected ? VibifyColors.primaryPurple : Colors.white,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Glass-themed empty state for library sections
class GlassLibraryEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final String? actionText;
  final VoidCallback? onActionTap;

  const GlassLibraryEmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.actionText,
    this.onActionTap,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GlassContainer.card(
        margin: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            if (actionText != null && onActionTap != null) ...[
              const SizedBox(height: 20),
              GlassContainer.button(
                onTap: onActionTap,
                child: Text(
                  actionText!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
