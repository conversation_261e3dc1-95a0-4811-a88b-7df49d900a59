import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../screens/full_player_screen.dart';
import '../../controllers/audio_controller.dart';
import '../../models/song.dart';

/// Glass-themed mini music player widget
class GlassMiniPlayer extends StatelessWidget {
  final VoidCallback? onTap;
  final bool showQueue;

  const GlassMiniPlayer({super.key, this.onTap, this.showQueue = false});

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioController>(
      builder: (context, audioController, child) {
        final currentSong = audioController.currentSong;
        if (currentSong == null) return const SizedBox.shrink();

        return GlassContainer(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(12),
          borderRadius: BorderRadius.circular(16),
          onTap:
              onTap ??
              () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FullPlayerScreen(),
                  ),
                );
              },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Progress bar
              _buildProgressBar(audioController),
              const SizedBox(height: 12),

              // Player controls
              Row(
                children: [
                  // Album art
                  _buildAlbumArt(currentSong),
                  const SizedBox(width: 12),

                  // Song info
                  Expanded(child: _buildSongInfo(currentSong)),

                  // Controls
                  _buildControls(audioController),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressBar(AudioController audioController) {
    return StreamBuilder<Duration>(
      stream: audioController.positionStream,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        final duration = audioController.duration;
        final progress = duration.inMilliseconds > 0
            ? position.inMilliseconds / duration.inMilliseconds
            : 0.0;

        return Container(
          height: 3,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(1.5),
            color: VibifyColors.seekBarInactive,
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress.clamp(0.0, 1.0),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(1.5),
                gradient: LinearGradient(colors: VibifyColors.purpleGradient),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAlbumArt(Song song) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(colors: VibifyColors.purpleGradient),
      ),
      child: song.primaryImageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                song.primaryImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildDefaultArt(),
              ),
            )
          : _buildDefaultArt(),
    );
  }

  Widget _buildDefaultArt() {
    return const Icon(Icons.music_note, color: Colors.white, size: 24);
  }

  Widget _buildSongInfo(Song song) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          song.title,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Text(
          song.artist,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildControls(AudioController audioController) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Previous button
        GlassContainer.button(
          padding: const EdgeInsets.all(8),
          onTap: audioController.hasPrevious
              ? audioController.skipToPrevious
              : null,
          child: Icon(
            Icons.skip_previous,
            size: 20,
            color: audioController.hasPrevious
                ? VibifyColors.textOnGlass
                : VibifyColors.textOnGlass.withValues(alpha: 0.5),
          ),
        ),
        const SizedBox(width: 8),

        // Play/Pause button
        GlassContainer.button(
          padding: const EdgeInsets.all(8),
          onTap: audioController.togglePlayPause,
          child: Icon(
            audioController.isPlaying ? Icons.pause : Icons.play_arrow,
            size: 24,
            color: VibifyColors.textOnGlass,
          ),
        ),
        const SizedBox(width: 8),

        // Next button
        GlassContainer.button(
          padding: const EdgeInsets.all(8),
          onTap: audioController.hasNext ? audioController.skipToNext : null,
          child: Icon(
            Icons.skip_next,
            size: 20,
            color: audioController.hasNext
                ? VibifyColors.textOnGlass
                : VibifyColors.textOnGlass.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }
}

/// Full-screen glass music player
class GlassFullPlayer extends StatefulWidget {
  const GlassFullPlayer({super.key});

  @override
  State<GlassFullPlayer> createState() => _GlassFullPlayerState();
}

class _GlassFullPlayerState extends State<GlassFullPlayer> {
  bool _isDragging = false;
  double _dragValue = 0.0;

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioController>(
      builder: (context, audioController, child) {
        final currentSong = audioController.currentSong;
        if (currentSong == null) {
          return const Scaffold(body: Center(child: Text('No song playing')));
        }

        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: GlassContainer.button(
              margin: const EdgeInsets.only(left: 16),
              padding: const EdgeInsets.all(8),
              onTap: () => Navigator.pop(context),
              child: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
            ),
            actions: [
              GlassContainer.button(
                margin: const EdgeInsets.only(right: 16),
                padding: const EdgeInsets.all(8),
                child: const Icon(Icons.more_vert, color: Colors.white),
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  VibifyColors.primaryPurple.withValues(alpha: 0.8),
                  VibifyColors.primaryBlue.withValues(alpha: 0.6),
                  VibifyColors.primaryPink.withValues(alpha: 0.4),
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    const Spacer(),

                    // Album art
                    _buildLargeAlbumArt(currentSong),
                    const SizedBox(height: 32),

                    // Song info
                    _buildSongInfo(currentSong),
                    const SizedBox(height: 32),

                    // Progress slider
                    _buildProgressSlider(audioController),
                    const SizedBox(height: 32),

                    // Main controls
                    _buildMainControls(audioController),
                    const SizedBox(height: 24),

                    // Secondary controls
                    _buildSecondaryControls(audioController),
                    const Spacer(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLargeAlbumArt(Song song) {
    return GlassContainer(
      width: 280,
      height: 280,
      borderRadius: BorderRadius.circular(20),
      child: song.primaryImageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.network(
                song.primaryImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildDefaultLargeArt(),
              ),
            )
          : _buildDefaultLargeArt(),
    );
  }

  Widget _buildDefaultLargeArt() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(colors: VibifyColors.purpleGradient),
      ),
      child: const Icon(Icons.music_note, color: Colors.white, size: 80),
    );
  }

  Widget _buildSongInfo(Song song) {
    return Column(
      children: [
        Text(
          song.title,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          song.artist,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (song.album != null) ...[
          const SizedBox(height: 4),
          Text(
            song.album!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildProgressSlider(AudioController audioController) {
    return StreamBuilder<Duration>(
      stream: audioController.positionStream,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        final duration = audioController.duration;
        final progress = duration.inMilliseconds > 0
            ? position.inMilliseconds / duration.inMilliseconds
            : 0.0;

        return Column(
          children: [
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.white,
                inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                thumbColor: Colors.white,
                overlayColor: Colors.white.withValues(alpha: 0.2),
                trackHeight: 4,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
              ),
              child: Slider(
                value: _isDragging ? _dragValue : progress.clamp(0.0, 1.0),
                onChanged: (value) {
                  setState(() {
                    _isDragging = true;
                    _dragValue = value;
                  });
                },
                onChangeEnd: (value) {
                  final newPosition = Duration(
                    milliseconds: (value * duration.inMilliseconds).round(),
                  );
                  audioController.seekTo(newPosition);
                  setState(() {
                    _isDragging = false;
                  });
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatDuration(position),
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _formatDuration(duration),
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMainControls(AudioController audioController) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Previous
        GlassContainer.button(
          padding: const EdgeInsets.all(16),
          onTap: audioController.hasPrevious
              ? audioController.skipToPrevious
              : null,
          child: Icon(
            Icons.skip_previous,
            size: 32,
            color: audioController.hasPrevious
                ? Colors.white
                : Colors.white.withValues(alpha: 0.5),
          ),
        ),

        // Play/Pause
        GlassContainer.button(
          padding: const EdgeInsets.all(20),
          onTap: audioController.togglePlayPause,
          child: Icon(
            audioController.isPlaying ? Icons.pause : Icons.play_arrow,
            size: 40,
            color: Colors.white,
          ),
        ),

        // Next
        GlassContainer.button(
          padding: const EdgeInsets.all(16),
          onTap: audioController.hasNext ? audioController.skipToNext : null,
          child: Icon(
            Icons.skip_next,
            size: 32,
            color: audioController.hasNext
                ? Colors.white
                : Colors.white.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondaryControls(AudioController audioController) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Shuffle
        GlassContainer.button(
          padding: const EdgeInsets.all(12),
          onTap: audioController.toggleShuffle,
          child: Icon(
            Icons.shuffle,
            size: 24,
            color: audioController.isShuffleEnabled
                ? VibifyColors.primaryPurple
                : Colors.white.withValues(alpha: 0.7),
          ),
        ),

        // Repeat
        GlassContainer.button(
          padding: const EdgeInsets.all(12),
          onTap: audioController.toggleRepeatMode,
          child: Icon(
            audioController.repeatMode == RepeatMode.one
                ? Icons.repeat_one
                : Icons.repeat,
            size: 24,
            color: audioController.repeatMode != RepeatMode.off
                ? VibifyColors.primaryPurple
                : Colors.white.withValues(alpha: 0.7),
          ),
        ),

        // Queue
        GlassContainer.button(
          padding: const EdgeInsets.all(12),
          child: Icon(
            Icons.queue_music,
            size: 24,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ),

        // Volume
        GlassContainer.button(
          padding: const EdgeInsets.all(12),
          child: Icon(
            Icons.volume_up,
            size: 24,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
