# Widgets Directory

This directory contains reusable UI widgets and components.

## Planned Widgets:

- `glass_app_bar.dart` - Custom AppBar with glass effect
- `glass_bottom_nav.dart` - Bottom navigation with glass theme
- `song_tile.dart` - Reusable song list item
- `playlist_tile.dart` - Playlist list item
- `download_progress.dart` - Download progress indicator
- `audio_visualizer.dart` - Optional audio visualization
- `seek_bar.dart` - Custom seek bar with animations
- `glass_dialog.dart` - Glass-themed dialogs
- `glass_button.dart` - Glass-themed buttons
- `loading_indicator.dart` - Custom loading animations

All widgets will:
- Follow the liquid glass design system
- Be fully responsive and accessible
- Handle overflow properly
- Include proper documentation
