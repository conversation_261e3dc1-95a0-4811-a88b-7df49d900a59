import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_library_widgets.dart';
import '../widgets/glass_download_widgets.dart';
import '../../models/song.dart';
import '../../controllers/search_controller.dart' as search_ctrl;
import '../../controllers/audio_controller.dart';

/// Glass-themed search bar with suggestions
class GlassSearchBar extends StatefulWidget {
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onClear;
  final TextEditingController? controller;
  final bool showSuggestions;

  const GlassSearchBar({
    super.key,
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.controller,
    this.showSuggestions = true,
  });

  @override
  State<GlassSearchBar> createState() => _GlassSearchBarState();
}

class _GlassSearchBarState extends State<GlassSearchBar> {
  late TextEditingController _controller;
  bool _hasText = false;
  final bool _showSuggestions = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
    _hasText = _controller.text.isNotEmpty;
  }

  @override
  void dispose() {
    _removeOverlay();
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }

    widget.onChanged?.call(_controller.text);

    if (widget.showSuggestions && hasText) {
      _showSuggestionsOverlay();
    } else {
      _removeOverlay();
    }
  }

  void _onClear() {
    _controller.clear();
    _removeOverlay();
    widget.onClear?.call();
  }

  void _showSuggestionsOverlay() {
    _removeOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 120, // Adjust based on your app bar height
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Consumer<search_ctrl.SearchController>(
            builder: (context, searchController, child) {
              searchController.getSuggestions(_controller.text);
              final suggestions = searchController.suggestions;

              if (suggestions.isEmpty) return const SizedBox.shrink();

              return GlassContainer(
                borderRadius: BorderRadius.circular(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: suggestions.take(5).map((suggestion) {
                    return ListTile(
                      leading: Icon(
                        searchController.searchHistory.contains(suggestion)
                            ? Icons.history
                            : Icons.search,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 20,
                      ),
                      title: Text(
                        suggestion,
                        style: const TextStyle(color: Colors.white),
                      ),
                      onTap: () {
                        _controller.text = suggestion;
                        _removeOverlay();
                        widget.onSubmitted?.call(suggestion);
                      },
                    );
                  }).toList(),
                ),
              );
            },
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      borderRadius: BorderRadius.circular(25),
      child: Row(
        children: [
          Icon(Icons.search, color: Colors.grey[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _controller,
              onSubmitted: (value) {
                _removeOverlay();
                widget.onSubmitted?.call(value);
              },
              style: const TextStyle(fontSize: 16, color: Colors.white),
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Search for music...',
                hintStyle: TextStyle(color: Colors.grey[600]),
                border: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          if (_hasText)
            GestureDetector(
              onTap: _onClear,
              child: Icon(Icons.clear, color: Colors.grey[600], size: 20),
            ),
        ],
      ),
    );
  }
}

/// Glass-themed search result tile
class GlassSearchResultTile extends StatelessWidget {
  final Song song;
  final VoidCallback? onTap;
  final VoidCallback? onMoreTap;
  final bool showArtist;

  const GlassSearchResultTile({
    super.key,
    required this.song,
    this.onTap,
    this.onMoreTap,
    this.showArtist = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioController>(
      builder: (context, audioController, child) {
        final isPlaying = audioController.currentSong?.id == song.id;

        return GlassContainer.card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          onTap: onTap,
          child: Row(
            children: [
              // Album art
              _buildAlbumArt(),
              const SizedBox(width: 16),

              // Song info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      song.title,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: isPlaying
                            ? VibifyColors.primaryPurple
                            : Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (showArtist) ...[
                      const SizedBox(height: 4),
                      Text(
                        song.artist,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    if (song.duration != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        song.formattedDuration,
                        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                      ),
                    ],
                  ],
                ),
              ),

              // Play button
              GlassContainer.button(
                padding: const EdgeInsets.all(8),
                onTap: onTap,
                child: Icon(
                  isPlaying ? Icons.pause : Icons.play_arrow,
                  size: 24,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),

              // Download button
              GlassDownloadButton(song: song, size: 32),

              // More button
              if (onMoreTap != null) ...[
                const SizedBox(width: 8),
                GlassContainer.button(
                  padding: const EdgeInsets.all(8),
                  onTap: onMoreTap,
                  child: Icon(
                    Icons.more_vert,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildAlbumArt() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: song.primaryImageUrl != null
            ? null
            : LinearGradient(colors: VibifyColors.purpleGradient),
      ),
      child: song.primaryImageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                song.primaryImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildDefaultArt(),
              ),
            )
          : _buildDefaultArt(),
    );
  }

  Widget _buildDefaultArt() {
    return const Icon(Icons.music_note, color: Colors.white, size: 28);
  }
}

/// Glass-themed genre/mood chips
class GlassGenreChips extends StatelessWidget {
  final List<String> genres;
  final String? selectedGenre;
  final ValueChanged<String> onGenreSelected;
  final String title;

  const GlassGenreChips({
    super.key,
    required this.genres,
    this.selectedGenre,
    required this.onGenreSelected,
    this.title = 'Genres',
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: genres.length,
            itemBuilder: (context, index) {
              final genre = genres[index];
              final isSelected = genre == selectedGenre;

              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: GlassContainer(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  color: isSelected
                      ? VibifyColors.primaryPurple.withValues(alpha: 0.3)
                      : null,
                  onTap: () => onGenreSelected(genre),
                  child: Text(
                    genre,
                    style: TextStyle(
                      color: isSelected
                          ? VibifyColors.primaryPurple
                          : Colors.white,
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.w500,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Glass-themed trending music section
class GlassTrendingSection extends StatelessWidget {
  final List<Song> trendingSongs;
  final ValueChanged<Song>? onSongTap;
  final ValueChanged<Song>? onMoreTap;

  const GlassTrendingSection({
    super.key,
    required this.trendingSongs,
    this.onSongTap,
    this.onMoreTap,
  });

  @override
  Widget build(BuildContext context) {
    if (trendingSongs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassLibraryHeader(
          title: 'Trending Now',
          subtitle: 'Popular music right now',
        ),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: trendingSongs.take(10).length,
            itemBuilder: (context, index) {
              final song = trendingSongs[index];
              return Container(
                width: 140,
                margin: const EdgeInsets.only(right: 12),
                child: GlassContainer.card(
                  onTap: () => onSongTap?.call(song),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Album art
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient: song.primaryImageUrl != null
                                ? null
                                : LinearGradient(
                                    colors: VibifyColors.purpleGradient,
                                  ),
                          ),
                          child: song.primaryImageUrl != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    song.primaryImageUrl!,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            const Icon(
                                              Icons.music_note,
                                              color: Colors.white,
                                              size: 40,
                                            ),
                                  ),
                                )
                              : const Icon(
                                  Icons.music_note,
                                  color: Colors.white,
                                  size: 40,
                                ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Song info
                      Text(
                        song.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        song.artist,
                        style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Glass-themed search history section
class GlassSearchHistory extends StatelessWidget {
  final List<String> searchHistory;
  final ValueChanged<String>? onHistoryTap;
  final ValueChanged<String>? onHistoryRemove;
  final VoidCallback? onClearAll;

  const GlassSearchHistory({
    super.key,
    required this.searchHistory,
    this.onHistoryTap,
    this.onHistoryRemove,
    this.onClearAll,
  });

  @override
  Widget build(BuildContext context) {
    if (searchHistory.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassLibraryHeader(
          title: 'Recent Searches',
          trailing: onClearAll != null
              ? GlassContainer.button(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  onTap: onClearAll,
                  child: const Text(
                    'Clear All',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                )
              : null,
        ),
        ...searchHistory.take(5).map((query) {
          return GlassContainer.card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            onTap: () => onHistoryTap?.call(query),
            child: Row(
              children: [
                Icon(Icons.history, color: Colors.grey[600], size: 20),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    query,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                if (onHistoryRemove != null)
                  GlassContainer.button(
                    padding: const EdgeInsets.all(4),
                    onTap: () => onHistoryRemove?.call(query),
                    child: Icon(Icons.close, size: 16, color: Colors.grey[600]),
                  ),
              ],
            ),
          );
        }),
      ],
    );
  }
}

/// Glass-themed search categories
class GlassSearchCategories extends StatelessWidget {
  final search_ctrl.SearchCategory selectedCategory;
  final ValueChanged<search_ctrl.SearchCategory> onCategoryChanged;

  const GlassSearchCategories({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context) {
    final categories = [
      (search_ctrl.SearchCategory.all, 'All'),
      (search_ctrl.SearchCategory.songs, 'Songs'),
      (search_ctrl.SearchCategory.artists, 'Artists'),
      (search_ctrl.SearchCategory.albums, 'Albums'),
    ];

    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final (category, label) = categories[index];
          final isSelected = category == selectedCategory;

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: GlassContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              borderRadius: BorderRadius.circular(20),
              color: isSelected
                  ? VibifyColors.primaryPurple.withValues(alpha: 0.3)
                  : null,
              onTap: () => onCategoryChanged(category),
              child: Text(
                label,
                style: TextStyle(
                  color: isSelected ? VibifyColors.primaryPurple : Colors.white,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
