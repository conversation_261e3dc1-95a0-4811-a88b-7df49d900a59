import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';

/// A custom app bar with glass morphism effect
class GlassAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final double elevation;
  final Color? backgroundColor;
  final bool centerTitle;
  final double? titleSpacing;
  final double toolbarHeight;
  final PreferredSizeWidget? bottom;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final bool enableBlur;
  final double blurIntensity;

  const GlassAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.elevation = 0,
    this.backgroundColor,
    this.centerTitle = true,
    this.titleSpacing,
    this.toolbarHeight = kToolbarHeight,
    this.bottom,
    this.systemOverlayStyle,
    this.enableBlur = true,
    this.blurIntensity = 15.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    // Determine system overlay style
    final effectiveSystemOverlayStyle =
        systemOverlayStyle ??
        (brightness == Brightness.dark
            ? SystemUiOverlayStyle.light
            : SystemUiOverlayStyle.dark);

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: effectiveSystemOverlayStyle,
      child: Container(
        height: preferredSize.height,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: brightness == Brightness.dark
                ? [
                    VibifyColors.darkGradient[0].withOpacity(0.9),
                    VibifyColors.darkGradient[0].withOpacity(0.7),
                  ]
                : [
                    VibifyColors.lightGradient[0].withOpacity(0.9),
                    VibifyColors.lightGradient[0].withOpacity(0.7),
                  ],
          ),
        ),
        child: enableBlur
            ? ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: blurIntensity,
                    sigmaY: blurIntensity,
                  ),
                  child: _buildAppBarContent(context, brightness),
                ),
              )
            : _buildAppBarContent(context, brightness),
      ),
    );
  }

  Widget _buildAppBarContent(BuildContext context, Brightness brightness) {
    return Container(
      decoration: BoxDecoration(
        color:
            backgroundColor ??
            (brightness == Brightness.dark
                ? VibifyColors.glassDark.withOpacity(0.3)
                : VibifyColors.glassLight.withOpacity(0.3)),
        border: Border(
          bottom: BorderSide(
            color: brightness == Brightness.dark
                ? Colors.white.withOpacity(0.1)
                : Colors.white.withOpacity(0.3),
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Column(
          children: [
            Container(
              height: toolbarHeight,
              padding: EdgeInsets.symmetric(horizontal: titleSpacing ?? 16.0),
              child: Row(
                children: [
                  // Leading widget
                  if (leading != null)
                    leading!
                  else if (automaticallyImplyLeading &&
                      Navigator.canPop(context))
                    GlassContainer.button(
                      padding: const EdgeInsets.all(8),
                      onTap: () => Navigator.pop(context),
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        size: 20,
                        color: VibifyColors.getTextColor(brightness),
                      ),
                    ),

                  // Title
                  Expanded(
                    child: centerTitle
                        ? Center(child: _buildTitle(brightness))
                        : _buildTitle(brightness),
                  ),

                  // Actions
                  if (actions != null) ...actions!,
                ],
              ),
            ),

            // Bottom widget (like TabBar)
            if (bottom != null) bottom!,
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(Brightness brightness) {
    if (titleWidget != null) return titleWidget!;

    if (title != null) {
      return Text(
        title!,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: VibifyColors.getTextColor(brightness),
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Size get preferredSize =>
      Size.fromHeight(toolbarHeight + (bottom?.preferredSize.height ?? 0.0));
}

/// Glass-themed action button for app bar
class GlassAppBarAction extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final double size;
  final Color? color;
  final bool showBadge;
  final String? badgeText;

  const GlassAppBarAction({
    super.key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.size = 24,
    this.color,
    this.showBadge = false,
    this.badgeText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    Widget iconButton = GlassContainer.button(
      padding: const EdgeInsets.all(8),
      onTap: onPressed,
      child: Icon(
        icon,
        size: size,
        color: color ?? VibifyColors.getTextColor(brightness),
      ),
    );

    if (showBadge) {
      iconButton = Badge(
        label: badgeText != null ? Text(badgeText!) : null,
        backgroundColor: VibifyColors.error,
        textColor: Colors.white,
        child: iconButton,
      );
    }

    if (tooltip != null) {
      iconButton = Tooltip(message: tooltip!, child: iconButton);
    }

    return Padding(padding: const EdgeInsets.only(left: 8), child: iconButton);
  }
}

/// Glass-themed search bar for app bar
class GlassAppBarSearchBar extends StatefulWidget {
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onClear;
  final TextEditingController? controller;
  final bool autofocus;

  const GlassAppBarSearchBar({
    super.key,
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.controller,
    this.autofocus = false,
  });

  @override
  State<GlassAppBarSearchBar> createState() => _GlassAppBarSearchBarState();
}

class _GlassAppBarSearchBarState extends State<GlassAppBarSearchBar> {
  late TextEditingController _controller;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
    _hasText = _controller.text.isNotEmpty;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
    widget.onChanged?.call(_controller.text);
  }

  void _onClear() {
    _controller.clear();
    widget.onClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return GlassContainer(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      borderRadius: BorderRadius.circular(25),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: VibifyColors.getTextColor(brightness, isPrimary: false),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _controller,
              autofocus: widget.autofocus,
              onSubmitted: widget.onSubmitted,
              style: TextStyle(
                color: VibifyColors.getTextColor(brightness),
                fontSize: 16,
              ),
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Search...',
                hintStyle: TextStyle(
                  color: VibifyColors.getTextColor(
                    brightness,
                    isPrimary: false,
                  ),
                ),
                border: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          if (_hasText)
            GestureDetector(
              onTap: _onClear,
              child: Icon(
                Icons.clear,
                color: VibifyColors.getTextColor(brightness, isPrimary: false),
                size: 20,
              ),
            ),
        ],
      ),
    );
  }
}
