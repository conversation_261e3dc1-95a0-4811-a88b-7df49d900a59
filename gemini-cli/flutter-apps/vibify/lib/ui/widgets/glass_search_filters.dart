import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';

/// Search filter options
enum SearchFilter { all, songs, albums, artists, playlists }

/// Search sort options
enum SearchSort { relevance, newest, oldest, duration, popularity }

/// Glass-themed search filters widget
class GlassSearchFilters extends StatelessWidget {
  final SearchFilter selectedFilter;
  final SearchSort selectedSort;
  final ValueChanged<SearchFilter>? onFilterChanged;
  final ValueChanged<SearchSort>? onSortChanged;
  final VoidCallback? onClearFilters;

  const GlassSearchFilters({
    super.key,
    required this.selectedFilter,
    required this.selectedSort,
    this.onFilterChanged,
    this.onSortChanged,
    this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer.card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Text(
                'Filters',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              if (selectedFilter != SearchFilter.all ||
                  selectedSort != SearchSort.relevance)
                GlassContainer.button(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  onTap: onClearFilters,
                  child: const Text(
                    'Clear',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Filter chips
          const Text(
            'Content Type',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: SearchFilter.values.map((filter) {
              final isSelected = selectedFilter == filter;
              return GlassContainer.button(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                onTap: () => onFilterChanged?.call(filter),
                child: Text(
                  _getFilterName(filter),
                  style: TextStyle(
                    color: isSelected
                        ? VibifyColors.primaryPurple
                        : Colors.white.withValues(alpha: 0.8),
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // Sort options
          const Text(
            'Sort By',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: SearchSort.values.map((sort) {
              final isSelected = selectedSort == sort;
              return GlassContainer.button(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                onTap: () => onSortChanged?.call(sort),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getSortIcon(sort),
                      size: 16,
                      color: isSelected
                          ? VibifyColors.primaryPurple
                          : Colors.white.withValues(alpha: 0.8),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      _getSortName(sort),
                      style: TextStyle(
                        color: isSelected
                            ? VibifyColors.primaryPurple
                            : Colors.white.withValues(alpha: 0.8),
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  String _getFilterName(SearchFilter filter) {
    switch (filter) {
      case SearchFilter.all:
        return 'All';
      case SearchFilter.songs:
        return 'Songs';
      case SearchFilter.albums:
        return 'Albums';
      case SearchFilter.artists:
        return 'Artists';
      case SearchFilter.playlists:
        return 'Playlists';
    }
  }

  String _getSortName(SearchSort sort) {
    switch (sort) {
      case SearchSort.relevance:
        return 'Relevance';
      case SearchSort.newest:
        return 'Newest';
      case SearchSort.oldest:
        return 'Oldest';
      case SearchSort.duration:
        return 'Duration';
      case SearchSort.popularity:
        return 'Popularity';
    }
  }

  IconData _getSortIcon(SearchSort sort) {
    switch (sort) {
      case SearchSort.relevance:
        return Icons.star;
      case SearchSort.newest:
        return Icons.new_releases;
      case SearchSort.oldest:
        return Icons.history;
      case SearchSort.duration:
        return Icons.timer;
      case SearchSort.popularity:
        return Icons.trending_up;
    }
  }
}

/// Glass-themed search suggestions widget
class GlassSearchSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final ValueChanged<String>? onSuggestionTap;
  final VoidCallback? onClearHistory;

  const GlassSearchSuggestions({
    super.key,
    required this.suggestions,
    this.onSuggestionTap,
    this.onClearHistory,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return GlassContainer.card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Text(
                'Recent Searches',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              if (suggestions.isNotEmpty)
                GlassContainer.button(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  onTap: onClearHistory,
                  child: const Text(
                    'Clear',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // Suggestions list
          ...suggestions.take(5).map((suggestion) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: GlassContainer.button(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                onTap: () => onSuggestionTap?.call(suggestion),
                child: Row(
                  children: [
                    Icon(
                      Icons.history,
                      size: 20,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        suggestion,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.north_west,
                      size: 16,
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}

/// Glass-themed trending searches widget
class GlassTrendingSearches extends StatelessWidget {
  final List<String> trending;
  final ValueChanged<String>? onTrendingTap;

  const GlassTrendingSearches({
    super.key,
    required this.trending,
    this.onTrendingTap,
  });

  @override
  Widget build(BuildContext context) {
    if (trending.isEmpty) {
      return const SizedBox.shrink();
    }

    return GlassContainer.card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: VibifyColors.primaryPurple,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Trending Now',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Trending list
          ...trending.take(5).toList().asMap().entries.map((entry) {
            final index = entry.key;
            final trend = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: GlassContainer.button(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                onTap: () => onTrendingTap?.call(trend),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: VibifyColors.purpleGradient,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        trend,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.trending_up,
                      size: 16,
                      color: VibifyColors.primaryPurple,
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
