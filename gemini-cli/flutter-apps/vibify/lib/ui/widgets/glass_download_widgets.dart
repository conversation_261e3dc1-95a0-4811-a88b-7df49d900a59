import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../../models/download.dart';
import '../../models/song.dart';
import '../../controllers/download_controller.dart';

/// Glass-themed download progress indicator
class GlassDownloadProgress extends StatelessWidget {
  final Download download;
  final double size;
  final bool showPercentage;

  const GlassDownloadProgress({
    super.key,
    required this.download,
    this.size = 48,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: VibifyColors.glassLight.withValues(alpha: 0.3),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
          ),
          
          // Progress circle
          SizedBox(
            width: size - 4,
            height: size - 4,
            child: CircularProgressIndicator(
              value: download.progress,
              strokeWidth: 3,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(download.status),
              ),
            ),
          ),
          
          // Center content
          _buildCenterContent(),
        ],
      ),
    );
  }

  Widget _buildCenterContent() {
    switch (download.status) {
      case DownloadStatus.pending:
        return Icon(
          Icons.schedule,
          size: size * 0.3,
          color: VibifyColors.downloadPending,
        );
      case DownloadStatus.downloading:
        if (showPercentage) {
          return Text(
            '${download.progressPercentage}%',
            style: TextStyle(
              fontSize: size * 0.2,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        } else {
          return Icon(
            Icons.download,
            size: size * 0.3,
            color: VibifyColors.downloadActive,
          );
        }
      case DownloadStatus.completed:
        return Icon(
          Icons.check,
          size: size * 0.3,
          color: VibifyColors.downloadComplete,
        );
      case DownloadStatus.failed:
        return Icon(
          Icons.error,
          size: size * 0.3,
          color: VibifyColors.downloadError,
        );
      case DownloadStatus.cancelled:
        return Icon(
          Icons.cancel,
          size: size * 0.3,
          color: VibifyColors.downloadPaused,
        );
      case DownloadStatus.paused:
        return Icon(
          Icons.pause,
          size: size * 0.3,
          color: VibifyColors.downloadPaused,
        );
    }
  }

  Color _getProgressColor(DownloadStatus status) {
    switch (status) {
      case DownloadStatus.pending:
        return VibifyColors.downloadPending;
      case DownloadStatus.downloading:
        return VibifyColors.downloadActive;
      case DownloadStatus.completed:
        return VibifyColors.downloadComplete;
      case DownloadStatus.failed:
        return VibifyColors.downloadError;
      case DownloadStatus.cancelled:
      case DownloadStatus.paused:
        return VibifyColors.downloadPaused;
    }
  }
}

/// Glass-themed download tile for lists
class GlassDownloadTile extends StatelessWidget {
  final Download download;
  final VoidCallback? onTap;
  final bool showActions;

  const GlassDownloadTile({
    super.key,
    required this.download,
    this.onTap,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer.card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      onTap: onTap,
      child: Row(
        children: [
          // Progress indicator
          GlassDownloadProgress(
            download: download,
            size: 48,
            showPercentage: false,
          ),
          const SizedBox(width: 16),
          
          // Song info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  download.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  download.artist,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                _buildStatusInfo(),
              ],
            ),
          ),
          
          // Actions
          if (showActions) _buildActions(context),
        ],
      ),
    );
  }

  Widget _buildStatusInfo() {
    switch (download.status) {
      case DownloadStatus.downloading:
        return Row(
          children: [
            Text(
              '${download.progressPercentage}% • ${download.getDownloadSpeed()}',
              style: TextStyle(
                fontSize: 12,
                color: VibifyColors.downloadActive,
              ),
            ),
            if (download.totalBytes != null) ...[
              const Text(' • '),
              Text(
                download.formattedFileSize,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        );
      case DownloadStatus.completed:
        return Text(
          'Downloaded • ${download.formattedFileSize}',
          style: TextStyle(
            fontSize: 12,
            color: VibifyColors.downloadComplete,
          ),
        );
      case DownloadStatus.failed:
        return Text(
          'Failed: ${download.errorMessage ?? 'Unknown error'}',
          style: TextStyle(
            fontSize: 12,
            color: VibifyColors.downloadError,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        );
      case DownloadStatus.paused:
        return Text(
          'Paused • ${download.progressPercentage}%',
          style: TextStyle(
            fontSize: 12,
            color: VibifyColors.downloadPaused,
          ),
        );
      case DownloadStatus.pending:
        return Text(
          'Waiting to download...',
          style: TextStyle(
            fontSize: 12,
            color: VibifyColors.downloadPending,
          ),
        );
      case DownloadStatus.cancelled:
        return Text(
          'Cancelled',
          style: TextStyle(
            fontSize: 12,
            color: VibifyColors.downloadPaused,
          ),
        );
    }
  }

  Widget _buildActions(BuildContext context) {
    final downloadController = Provider.of<DownloadController>(context, listen: false);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (download.isActive) ...[
          // Pause button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.pauseDownload(download.id),
            child: const Icon(Icons.pause, size: 20, color: Colors.white),
          ),
          const SizedBox(width: 8),
          // Cancel button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.cancelDownload(download.id),
            child: const Icon(Icons.close, size: 20, color: Colors.white),
          ),
        ] else if (download.isPaused) ...[
          // Resume button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.resumeDownload(download.id),
            child: const Icon(Icons.play_arrow, size: 20, color: Colors.white),
          ),
          const SizedBox(width: 8),
          // Cancel button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.cancelDownload(download.id),
            child: const Icon(Icons.close, size: 20, color: Colors.white),
          ),
        ] else if (download.isFailed) ...[
          // Retry button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.retryDownload(download.id),
            child: const Icon(Icons.refresh, size: 20, color: Colors.white),
          ),
          const SizedBox(width: 8),
          // Remove button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.removeDownload(download.id),
            child: const Icon(Icons.delete, size: 20, color: Colors.white),
          ),
        ] else if (download.isCompleted) ...[
          // Remove button
          GlassContainer.button(
            padding: const EdgeInsets.all(8),
            onTap: () => downloadController.removeDownload(download.id),
            child: const Icon(Icons.delete, size: 20, color: Colors.white),
          ),
        ],
      ],
    );
  }
}

/// Glass-themed download button for songs
class GlassDownloadButton extends StatelessWidget {
  final Song song;
  final double size;

  const GlassDownloadButton({
    super.key,
    required this.song,
    this.size = 40,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DownloadController>(
      builder: (context, downloadController, child) {
        final download = downloadController.getDownloadForSong(song.id);
        
        if (download != null) {
          // Show progress if downloading
          if (download.isActive) {
            return GlassDownloadProgress(
              download: download,
              size: size,
              showPercentage: false,
            );
          }
          
          // Show status icon for other states
          return GlassContainer.button(
            padding: EdgeInsets.all(size * 0.2),
            onTap: () => _handleDownloadAction(context, downloadController, download),
            child: Icon(
              _getDownloadIcon(download.status),
              size: size * 0.5,
              color: _getDownloadIconColor(download.status),
            ),
          );
        }
        
        // Show download button if not downloaded
        return GlassContainer.button(
          padding: EdgeInsets.all(size * 0.2),
          onTap: () => downloadController.downloadSong(song),
          child: Icon(
            Icons.download,
            size: size * 0.5,
            color: Colors.white,
          ),
        );
      },
    );
  }

  void _handleDownloadAction(
    BuildContext context,
    DownloadController controller,
    Download download,
  ) {
    switch (download.status) {
      case DownloadStatus.completed:
        // Already downloaded, could show options
        break;
      case DownloadStatus.failed:
        controller.retryDownload(download.id);
        break;
      case DownloadStatus.paused:
        controller.resumeDownload(download.id);
        break;
      case DownloadStatus.cancelled:
        controller.retryDownload(download.id);
        break;
      default:
        break;
    }
  }

  IconData _getDownloadIcon(DownloadStatus status) {
    switch (status) {
      case DownloadStatus.completed:
        return Icons.download_done;
      case DownloadStatus.failed:
        return Icons.refresh;
      case DownloadStatus.paused:
        return Icons.play_arrow;
      case DownloadStatus.cancelled:
        return Icons.refresh;
      default:
        return Icons.download;
    }
  }

  Color _getDownloadIconColor(DownloadStatus status) {
    switch (status) {
      case DownloadStatus.completed:
        return VibifyColors.downloadComplete;
      case DownloadStatus.failed:
        return VibifyColors.downloadError;
      case DownloadStatus.paused:
        return VibifyColors.downloadPaused;
      case DownloadStatus.cancelled:
        return VibifyColors.downloadPaused;
      default:
        return Colors.white;
    }
  }
}

/// Glass-themed download stats widget
class GlassDownloadStats extends StatelessWidget {
  const GlassDownloadStats({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<DownloadController>(
      builder: (context, downloadController, child) {
        final stats = downloadController.downloadStats;
        
        return GlassContainer.card(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Download Statistics',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Stats grid
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Total',
                      stats['total']?.toString() ?? '0',
                      VibifyColors.primaryPurple,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Completed',
                      stats['completed']?.toString() ?? '0',
                      VibifyColors.downloadComplete,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Active',
                      stats['active']?.toString() ?? '0',
                      VibifyColors.downloadActive,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'Failed',
                      stats['failed']?.toString() ?? '0',
                      VibifyColors.downloadError,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Total size
              Text(
                'Total Downloaded: ${downloadController.getFormattedTotalSize()}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
