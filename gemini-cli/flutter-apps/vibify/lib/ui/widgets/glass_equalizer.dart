import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../../controllers/equalizer_controller.dart';

/// Glass-themed equalizer widget
class GlassEqualizer extends StatelessWidget {
  final bool showPresets;
  final bool showEffects;

  const GlassEqualizer({
    super.key,
    this.showPresets = true,
    this.showEffects = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<EqualizerController>(
      builder: (context, equalizerController, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with enable/disable toggle
            _buildHeader(equalizerController),

            const SizedBox(height: 16),

            // Presets section
            if (showPresets) ...[
              _buildPresetsSection(equalizerController),
              const SizedBox(height: 24),
            ],

            // Equalizer bands
            _buildEqualizerBands(context, equalizerController),

            const SizedBox(height: 24),

            // Audio effects section
            if (showEffects) ...[
              _buildEffectsSection(context, equalizerController),
            ],
          ],
        );
      },
    );
  }

  Widget _buildHeader(EqualizerController controller) {
    return Row(
      children: [
        const Text(
          'Equalizer',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const Spacer(),
        GlassContainer.button(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          onTap: controller.toggleEqualizer,
          child: Text(
            controller.isEnabled ? 'ON' : 'OFF',
            style: TextStyle(
              color: controller.isEnabled
                  ? VibifyColors.primaryPurple
                  : Colors.white.withValues(alpha: 0.7),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPresetsSection(EqualizerController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Presets',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: EqualizerPreset.values.length - 1, // Exclude custom
            itemBuilder: (context, index) {
              final preset = EqualizerPreset.values[index];
              final isSelected = controller.currentPreset == preset;

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: GlassContainer.button(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  onTap: controller.isEnabled
                      ? () => controller.applyPreset(preset)
                      : null,
                  child: Text(
                    controller.getPresetName(preset),
                    style: TextStyle(
                      color: isSelected
                          ? VibifyColors.primaryPurple
                          : Colors.white.withValues(
                              alpha: controller.isEnabled ? 0.9 : 0.5,
                            ),
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEqualizerBands(
    BuildContext context,
    EqualizerController controller,
  ) {
    return GlassContainer.card(
      child: Column(
        children: [
          // Frequency labels
          Row(
            children: EqualizerBand.values.map((band) {
              return Expanded(
                child: Text(
                  controller.getBandFrequency(band).replaceAll(' ', '\n'),
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // Equalizer sliders
          SizedBox(
            height: 200,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: EqualizerBand.values.map((band) {
                final level = controller.bandLevels[band] ?? 0.0;

                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    child: _buildEqualizerSlider(
                      context: context,
                      band: band,
                      level: level,
                      onChanged: controller.isEnabled
                          ? (value) => controller.setBandLevel(band, value)
                          : null,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

          const SizedBox(height: 16),

          // dB scale
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '-15dB',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white.withValues(alpha: 0.5),
                ),
              ),
              Text(
                '0dB',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
              Text(
                '+15dB',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white.withValues(alpha: 0.5),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEqualizerSlider({
    required BuildContext context,
    required EqualizerBand band,
    required double level,
    required ValueChanged<double>? onChanged,
  }) {
    return RotatedBox(
      quarterTurns: 3,
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
          activeTrackColor: VibifyColors.primaryPurple,
          inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
          thumbColor: VibifyColors.primaryPurple,
          overlayColor: VibifyColors.primaryPurple.withValues(alpha: 0.2),
          trackHeight: 4,
          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
        ),
        child: Slider(
          value: level,
          min: -15.0,
          max: 15.0,
          divisions: 60,
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildEffectsSection(
    BuildContext context,
    EqualizerController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Audio Effects',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),

        // Bass Boost
        _buildEffectControl(
          context: context,
          title: 'Bass Boost',
          enabled: controller.bassBoostEnabled,
          value: controller.bassBoostStrength,
          onToggle: controller.isEnabled
              ? (enabled) => controller.setBassBoost(
                  enabled,
                  strength: controller.bassBoostStrength,
                )
              : null,
          onChanged: controller.isEnabled && controller.bassBoostEnabled
              ? (value) => controller.setBassBoost(true, strength: value)
              : null,
        ),

        const SizedBox(height: 16),

        // Virtualizer
        _buildEffectControl(
          context: context,
          title: 'Virtualizer',
          enabled: controller.virtualizerEnabled,
          value: controller.virtualizerStrength,
          onToggle: controller.isEnabled
              ? (enabled) => controller.setVirtualizer(
                  enabled,
                  strength: controller.virtualizerStrength,
                )
              : null,
          onChanged: controller.isEnabled && controller.virtualizerEnabled
              ? (value) => controller.setVirtualizer(true, strength: value)
              : null,
        ),

        const SizedBox(height: 16),

        // Loudness Enhancer
        _buildEffectControl(
          context: context,
          title: 'Loudness Enhancer',
          enabled: controller.loudnessEnhancerEnabled,
          value: controller.loudnessGain / 2.0, // Scale to 0-1
          onToggle: controller.isEnabled
              ? (enabled) => controller.setLoudnessEnhancer(
                  enabled,
                  gain: controller.loudnessGain,
                )
              : null,
          onChanged: controller.isEnabled && controller.loudnessEnhancerEnabled
              ? (value) =>
                    controller.setLoudnessEnhancer(true, gain: value * 2.0)
              : null,
          valueFormatter: (value) => '${(value * 200).round()}%',
        ),
      ],
    );
  }

  Widget _buildEffectControl({
    required BuildContext context,
    required String title,
    required bool enabled,
    required double value,
    required ValueChanged<bool>? onToggle,
    required ValueChanged<double>? onChanged,
    String Function(double)? valueFormatter,
  }) {
    return GlassContainer.card(
      child: Column(
        children: [
          Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                valueFormatter?.call(value) ?? '${(value * 100).round()}%',
                style: TextStyle(
                  fontSize: 14,
                  color: VibifyColors.primaryPurple,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 12),
              GlassContainer.button(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                onTap: onToggle != null ? () => onToggle(!enabled) : null,
                child: Text(
                  enabled ? 'ON' : 'OFF',
                  style: TextStyle(
                    color: enabled
                        ? VibifyColors.primaryPurple
                        : Colors.white.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          if (enabled) ...[
            const SizedBox(height: 12),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: VibifyColors.primaryPurple,
                inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                thumbColor: VibifyColors.primaryPurple,
                overlayColor: VibifyColors.primaryPurple.withValues(alpha: 0.2),
                trackHeight: 4,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
              ),
              child: Slider(
                value: value,
                min: 0.0,
                max: 1.0,
                divisions: 20,
                onChanged: onChanged,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
