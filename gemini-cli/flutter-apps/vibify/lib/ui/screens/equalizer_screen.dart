import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../widgets/glass_app_bar.dart';
import '../widgets/glass_equalizer.dart';
import '../../controllers/equalizer_controller.dart';

/// Equalizer screen with advanced audio controls
class EqualizerScreen extends StatefulWidget {
  const EqualizerScreen({super.key});

  @override
  State<EqualizerScreen> createState() => _EqualizerScreenState();
}

class _EqualizerScreenState extends State<EqualizerScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              // App bar
              GlassAppBar(
                title: 'Equalizer',
                actions: [
                  GlassAppBarAction(
                    icon: Icons.restore,
                    onPressed: _showResetDialog,
                    tooltip: 'Reset to defaults',
                  ),
                ],
              ),
              
              // Equalizer content
              Expanded(
                child: Consumer<EqualizerController>(
                  builder: (context, equalizerController, child) {
                    return SingleChildScrollView(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      child: const GlassEqualizer(
                        showPresets: true,
                        showEffects: true,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.transparent,
        content: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: VibifyColors.glassLight.withValues(alpha: 0.1),
            border: Border.all(
              color: VibifyColors.glassLight.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Reset Equalizer',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This will reset all equalizer settings and audio effects to their default values.',
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: VibifyColors.glassLight.withValues(alpha: 0.1),
                        border: Border.all(
                          color: VibifyColors.glassLight.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text(
                          'Cancel',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: VibifyColors.glassLight.withValues(alpha: 0.1),
                        border: Border.all(
                          color: VibifyColors.glassLight.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          final controller = Provider.of<EqualizerController>(context, listen: false);
                          controller.resetToDefault();
                        },
                        child: Text(
                          'Reset',
                          style: TextStyle(color: VibifyColors.primaryPurple),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
