import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_music_player.dart';
import '../../controllers/audio_controller.dart';
import '../../controllers/library_controller.dart';
import '../../models/song.dart';

/// Enhanced full-screen player with visualizations and advanced controls
class FullPlayerScreen extends StatefulWidget {
  const FullPlayerScreen({super.key});

  @override
  State<FullPlayerScreen> createState() => _FullPlayerScreenState();
}

class _FullPlayerScreenState extends State<FullPlayerScreen>
    with TickerProviderStateMixin {
  late AnimationController _visualizerController;
  late AnimationController _albumArtController;
  late Animation<double> _albumArtRotation;

  bool _showLyrics = false;
  bool _showQueue = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _visualizerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _albumArtController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );

    _albumArtRotation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _albumArtController, curve: Curves.linear),
    );
  }

  @override
  void dispose() {
    _visualizerController.dispose();
    _albumArtController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Consumer<AudioController>(
        builder: (context, audioController, child) {
          final song = audioController.currentSong;

          // Control album art rotation based on playback state
          if (audioController.isPlaying) {
            _albumArtController.repeat();
          } else {
            _albumArtController.stop();
          }

          return Container(
            decoration: BoxDecoration(gradient: _buildDynamicGradient(song)),
            child: SafeArea(
              child: Column(
                children: [
                  // Top bar
                  _buildTopBar(context),

                  // Main content
                  Expanded(
                    child: _showQueue
                        ? _buildQueueView(audioController)
                        : _showLyrics
                        ? _buildLyricsView(song)
                        : _buildPlayerView(audioController, song),
                  ),

                  // Bottom controls
                  _buildBottomControls(audioController),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: () => Navigator.pop(context),
            child: const Icon(
              Icons.keyboard_arrow_down,
              color: Colors.white,
              size: 28,
            ),
          ),
          const Spacer(),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: () => setState(() => _showQueue = !_showQueue),
            child: Icon(
              _showQueue ? Icons.music_note : Icons.queue_music,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: _showMoreOptions,
            child: const Icon(Icons.more_vert, color: Colors.white, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerView(AudioController audioController, Song? song) {
    if (song == null) {
      return const Center(
        child: Text(
          'No song playing',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      );
    }

    return Column(
      children: [
        // Visualizer
        _buildVisualizer(audioController),

        const SizedBox(height: 32),

        // Album art
        _buildAlbumArt(song),

        const SizedBox(height: 32),

        // Song info
        _buildSongInfo(song),

        const SizedBox(height: 24),

        // Progress bar
        _buildProgressBar(audioController),

        const SizedBox(height: 32),

        // Main controls
        _buildMainControls(audioController),

        const SizedBox(height: 24),

        // Secondary controls
        _buildSecondaryControls(audioController),
      ],
    );
  }

  Widget _buildVisualizer(AudioController audioController) {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 32),
      child: AnimatedBuilder(
        animation: _visualizerController,
        builder: (context, child) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: List.generate(20, (index) {
              final height = audioController.isPlaying
                  ? (math.sin(
                              (_visualizerController.value * 2 * math.pi) +
                                  (index * 0.5),
                            ) +
                            1) *
                        30
                  : 5.0;

              return Container(
                width: 3,
                height: height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      VibifyColors.primaryPurple,
                      VibifyColors.primaryPurple.withValues(alpha: 0.5),
                    ],
                  ),
                ),
              );
            }),
          );
        },
      ),
    );
  }

  Widget _buildAlbumArt(Song song) {
    return Center(
      child: AnimatedBuilder(
        animation: _albumArtRotation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _albumArtRotation.value,
            child: Container(
              width: 280,
              height: 280,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: GlassContainer(
                borderRadius: BorderRadius.circular(140),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(140),
                  child: song.primaryImageUrl != null
                      ? Image.network(
                          song.primaryImageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildDefaultAlbumArt(),
                        )
                      : _buildDefaultAlbumArt(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDefaultAlbumArt() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: VibifyColors.purpleGradient),
      ),
      child: const Icon(Icons.music_note, color: Colors.white, size: 80),
    );
  }

  Widget _buildSongInfo(Song song) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        children: [
          Text(
            song.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            song.artist,
            style: TextStyle(
              fontSize: 18,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (song.album != null) ...[
            const SizedBox(height: 4),
            Text(
              song.album!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressBar(AudioController audioController) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        children: [
          StreamBuilder<Duration>(
            stream: audioController.positionStream,
            builder: (context, snapshot) {
              final position = snapshot.data ?? Duration.zero;
              final duration = audioController.duration;
              final progress = duration.inMilliseconds > 0
                  ? position.inMilliseconds / duration.inMilliseconds
                  : 0.0;

              return SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.white,
                  inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                  thumbColor: Colors.white,
                  overlayColor: Colors.white.withValues(alpha: 0.2),
                  trackHeight: 4,
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 8,
                  ),
                ),
                child: Slider(
                  value: progress.clamp(0.0, 1.0),
                  onChanged: (value) {
                    final newPosition = Duration(
                      milliseconds: (value * duration.inMilliseconds).round(),
                    );
                    audioController.seekTo(newPosition);
                  },
                ),
              );
            },
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              StreamBuilder<Duration>(
                stream: audioController.positionStream,
                builder: (context, snapshot) {
                  final position = snapshot.data ?? Duration.zero;
                  return Text(
                    _formatDuration(position),
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                  );
                },
              ),
              Text(
                _formatDuration(audioController.duration),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainControls(AudioController audioController) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        GlassContainer.button(
          padding: const EdgeInsets.all(16),
          onTap: audioController.hasPrevious
              ? audioController.skipToPrevious
              : null,
          child: Icon(
            Icons.skip_previous,
            color: audioController.hasPrevious
                ? Colors.white
                : Colors.white.withValues(alpha: 0.5),
            size: 36,
          ),
        ),
        GlassContainer.button(
          padding: const EdgeInsets.all(20),
          onTap: audioController.isPlaying
              ? audioController.pause
              : audioController.play,
          child: Icon(
            audioController.isPlaying ? Icons.pause : Icons.play_arrow,
            color: Colors.white,
            size: 48,
          ),
        ),
        GlassContainer.button(
          padding: const EdgeInsets.all(16),
          onTap: audioController.hasNext ? audioController.skipToNext : null,
          child: Icon(
            Icons.skip_next,
            color: audioController.hasNext
                ? Colors.white
                : Colors.white.withValues(alpha: 0.5),
            size: 36,
          ),
        ),
      ],
    );
  }

  Widget _buildSecondaryControls(AudioController audioController) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: audioController.toggleShuffle,
            child: Icon(
              Icons.shuffle,
              color: audioController.isShuffleEnabled
                  ? VibifyColors.primaryPurple
                  : Colors.white.withValues(alpha: 0.7),
              size: 24,
            ),
          ),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: () => _showVolumeSlider(audioController),
            child: Icon(
              Icons.volume_up,
              color: Colors.white.withValues(alpha: 0.7),
              size: 24,
            ),
          ),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: () => setState(() => _showLyrics = !_showLyrics),
            child: Icon(
              Icons.lyrics,
              color: _showLyrics
                  ? VibifyColors.primaryPurple
                  : Colors.white.withValues(alpha: 0.7),
              size: 24,
            ),
          ),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: audioController.toggleRepeatMode,
            child: Icon(
              audioController.repeatMode == RepeatMode.one
                  ? Icons.repeat_one
                  : Icons.repeat,
              color: audioController.repeatMode != RepeatMode.off
                  ? VibifyColors.primaryPurple
                  : Colors.white.withValues(alpha: 0.7),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls(AudioController audioController) {
    final song = audioController.currentSong;
    if (song == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Consumer<LibraryController>(
            builder: (context, libraryController, child) {
              return GlassContainer.button(
                padding: const EdgeInsets.all(12),
                onTap: () => libraryController.toggleFavorite(song.id),
                child: Icon(
                  song.isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: song.isFavorite
                      ? Colors.red
                      : Colors.white.withValues(alpha: 0.7),
                  size: 24,
                ),
              );
            },
          ),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: () => _showAddToPlaylist(song),
            child: Icon(
              Icons.playlist_add,
              color: Colors.white.withValues(alpha: 0.7),
              size: 24,
            ),
          ),
          GlassContainer.button(
            padding: const EdgeInsets.all(12),
            onTap: () => _showShareOptions(song),
            child: Icon(
              Icons.share,
              color: Colors.white.withValues(alpha: 0.7),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQueueView(AudioController audioController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'Up Next',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: audioController.queue.length,
            itemBuilder: (context, index) {
              final song = audioController.queue[index];
              final isCurrentSong = audioController.currentIndex == index;

              return GlassContainer.card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: ListTile(
                  leading: isCurrentSong
                      ? Icon(Icons.equalizer, color: VibifyColors.primaryPurple)
                      : Text(
                          '${index + 1}',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                  title: Text(
                    song.title,
                    style: TextStyle(
                      color: isCurrentSong
                          ? VibifyColors.primaryPurple
                          : Colors.white,
                      fontWeight: isCurrentSong
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    song.artist,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: GlassContainer.button(
                    padding: const EdgeInsets.all(8),
                    onTap: () {
                      // TODO: Implement remove from queue
                      debugPrint('Remove from queue: $index');
                    },
                    child: Icon(
                      Icons.close,
                      color: Colors.white.withValues(alpha: 0.7),
                      size: 20,
                    ),
                  ),
                  onTap: () {
                    // TODO: Implement skip to queue item
                    debugPrint('Skip to queue item: $index');
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLyricsView(Song? song) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Text(
          'Lyrics not available for this song',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 18,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  LinearGradient _buildDynamicGradient(Song? song) {
    // Create dynamic gradient based on song or use default
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        VibifyColors.darkGradient[0],
        VibifyColors.darkGradient[1],
        VibifyColors.primaryPurple.withValues(alpha: 0.3),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _showVolumeSlider(AudioController audioController) {
    // TODO: Implement volume slider modal
    debugPrint('Show volume slider');
  }

  void _showMoreOptions() {
    // TODO: Implement more options modal
    debugPrint('Show more options');
  }

  void _showAddToPlaylist(Song song) {
    // TODO: Implement add to playlist modal
    debugPrint('Add to playlist: ${song.title}');
  }

  void _showShareOptions(Song song) {
    // TODO: Implement share options
    debugPrint('Share song: ${song.title}');
  }
}
