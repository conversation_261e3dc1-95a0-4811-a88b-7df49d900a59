import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_app_bar.dart';
import '../widgets/glass_download_widgets.dart';
import '../../controllers/download_controller.dart';
import '../../models/download.dart';

/// Screen for managing downloads
class DownloadsScreen extends StatefulWidget {
  const DownloadsScreen({super.key});

  @override
  State<DownloadsScreen> createState() => _DownloadsScreenState();
}

class _DownloadsScreenState extends State<DownloadsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom app bar with tabs
              _buildAppBar(),
              
              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllDownloadsTab(),
                    _buildActiveDownloadsTab(),
                    _buildCompletedDownloadsTab(),
                    _buildStatsTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Column(
      children: [
        // App bar
        GlassAppBar(
          title: 'Downloads',
          actions: [
            Consumer<DownloadController>(
              builder: (context, downloadController, child) {
                return PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: (value) => _handleMenuAction(value, downloadController),
                  itemBuilder: (context) => [
                    if (downloadController.hasActiveDownloads)
                      const PopupMenuItem(
                        value: 'pause_all',
                        child: Text('Pause All'),
                      ),
                    if (downloadController.downloads.any((d) => d.isPaused))
                      const PopupMenuItem(
                        value: 'resume_all',
                        child: Text('Resume All'),
                      ),
                    if (downloadController.hasFailedDownloads)
                      const PopupMenuItem(
                        value: 'retry_all',
                        child: Text('Retry Failed'),
                      ),
                    if (downloadController.hasCompletedDownloads)
                      const PopupMenuItem(
                        value: 'clear_completed',
                        child: Text('Clear Completed'),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
        
        // Tab bar
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: GlassContainer(
            padding: const EdgeInsets.all(4),
            borderRadius: BorderRadius.circular(25),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                gradient: LinearGradient(
                  colors: VibifyColors.purpleGradient,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
              labelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              tabs: const [
                Tab(text: 'All'),
                Tab(text: 'Active'),
                Tab(text: 'Done'),
                Tab(text: 'Stats'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAllDownloadsTab() {
    return Consumer<DownloadController>(
      builder: (context, downloadController, child) {
        if (downloadController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (downloadController.downloads.isEmpty) {
          return _buildEmptyState('No downloads yet', 'Start downloading music to see them here');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: downloadController.downloads.length,
          itemBuilder: (context, index) {
            final download = downloadController.downloads[index];
            return GlassDownloadTile(
              download: download,
              onTap: () => _showDownloadDetails(download),
            );
          },
        );
      },
    );
  }

  Widget _buildActiveDownloadsTab() {
    return Consumer<DownloadController>(
      builder: (context, downloadController, child) {
        final activeDownloads = downloadController.activeDownloads;

        if (activeDownloads.isEmpty) {
          return _buildEmptyState('No active downloads', 'Downloads will appear here when in progress');
        }

        return Column(
          children: [
            // Overall progress
            if (activeDownloads.isNotEmpty) _buildOverallProgress(downloadController),
            
            // Active downloads list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: activeDownloads.length,
                itemBuilder: (context, index) {
                  final download = activeDownloads[index];
                  return GlassDownloadTile(
                    download: download,
                    onTap: () => _showDownloadDetails(download),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCompletedDownloadsTab() {
    return Consumer<DownloadController>(
      builder: (context, downloadController, child) {
        final completedDownloads = downloadController.completedDownloads;

        if (completedDownloads.isEmpty) {
          return _buildEmptyState('No completed downloads', 'Completed downloads will appear here');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: completedDownloads.length,
          itemBuilder: (context, index) {
            final download = completedDownloads[index];
            return GlassDownloadTile(
              download: download,
              onTap: () => _showDownloadDetails(download),
            );
          },
        );
      },
    );
  }

  Widget _buildStatsTab() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          GlassDownloadStats(),
          // Add more stats widgets here if needed
        ],
      ),
    );
  }

  Widget _buildOverallProgress(DownloadController downloadController) {
    final totalProgress = downloadController.getTotalProgress();
    
    return GlassContainer.card(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Overall Progress',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          
          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: VibifyColors.seekBarInactive,
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: totalProgress.clamp(0.0, 1.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  gradient: LinearGradient(
                    colors: VibifyColors.purpleGradient,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          Text(
            '${downloadController.activeCount} active downloads • ${(totalProgress * 100).round()}% complete',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: GlassContainer.card(
        margin: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.download_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action, DownloadController downloadController) {
    switch (action) {
      case 'pause_all':
        downloadController.pauseAllDownloads();
        break;
      case 'resume_all':
        downloadController.resumeAllDownloads();
        break;
      case 'retry_all':
        downloadController.retryAllFailedDownloads();
        break;
      case 'clear_completed':
        _showClearCompletedDialog(downloadController);
        break;
    }
  }

  void _showClearCompletedDialog(DownloadController downloadController) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.transparent,
        content: GlassContainer.dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Clear Completed Downloads',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This will remove all completed downloads from the list. The downloaded files will remain on your device.',
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: GlassContainer.button(
                      onTap: () => Navigator.pop(context),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: GlassContainer.button(
                      onTap: () {
                        downloadController.clearCompletedDownloads();
                        Navigator.pop(context);
                      },
                      child: const Text(
                        'Clear',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDownloadDetails(Download download) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => GlassContainer(
        margin: const EdgeInsets.all(16),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                download.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                download.artist,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withValues(alpha: 0.8),
                ),
              ),
              const SizedBox(height: 16),
              _buildDetailRow('Status', download.statusText),
              _buildDetailRow('Progress', '${download.progressPercentage}%'),
              if (download.totalBytes != null)
                _buildDetailRow('Size', download.formattedFileSize),
              if (download.isActive)
                _buildDetailRow('Speed', download.getDownloadSpeed()),
              if (download.errorMessage != null)
                _buildDetailRow('Error', download.errorMessage!),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
