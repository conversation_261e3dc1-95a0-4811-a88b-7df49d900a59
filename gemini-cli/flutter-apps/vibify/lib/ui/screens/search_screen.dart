import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_app_bar.dart';
import '../widgets/glass_search_widgets.dart';
import '../widgets/glass_library_widgets.dart';
import '../../controllers/search_controller.dart' as search_ctrl;
import '../../controllers/audio_controller.dart';
import '../../controllers/library_controller.dart';
import '../../models/song.dart';

/// Main search screen for discovering music
class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load trending music when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final searchController = Provider.of<search_ctrl.SearchController>(
        context,
        listen: false,
      );
      searchController.loadTrendingMusic();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Search bar
              _buildSearchHeader(),

              // Content
              Expanded(
                child: Consumer<search_ctrl.SearchController>(
                  builder: (context, searchController, child) {
                    if (searchController.hasQuery) {
                      return _buildSearchResults(searchController);
                    } else {
                      return _buildDiscoverContent(searchController);
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Column(
      children: [
        // App bar
        GlassAppBar(
          title: 'Search',
          actions: [
            GlassAppBarAction(
              icon: Icons.mic,
              onPressed: () {
                // TODO: Voice search
              },
              tooltip: 'Voice search',
            ),
          ],
        ),

        // Search bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: GlassSearchBar(
            controller: _searchController,
            hintText: 'Search for songs, artists, albums...',
            onChanged: (query) {
              final searchController =
                  Provider.of<search_ctrl.SearchController>(
                    context,
                    listen: false,
                  );
              searchController.searchMusic(query);
            },
            onSubmitted: (query) {
              final searchController =
                  Provider.of<search_ctrl.SearchController>(
                    context,
                    listen: false,
                  );
              searchController.searchMusic(query, immediate: true);
            },
            onClear: () {
              final searchController =
                  Provider.of<search_ctrl.SearchController>(
                    context,
                    listen: false,
                  );
              searchController.clearSearch();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults(search_ctrl.SearchController searchController) {
    return Column(
      children: [
        // Search categories
        GlassSearchCategories(
          selectedCategory: searchController.selectedCategory,
          onCategoryChanged: searchController.setSearchCategory,
        ),

        // Results
        Expanded(child: _buildResultsList(searchController)),
      ],
    );
  }

  Widget _buildResultsList(search_ctrl.SearchController searchController) {
    if (searchController.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (searchController.errorMessage != null) {
      return _buildErrorState(searchController.errorMessage!);
    }

    final results = searchController.getFilteredResults();

    if (results.isEmpty) {
      return _buildEmptySearchState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 100), // Space for mini player
      itemCount: results.length,
      itemBuilder: (context, index) {
        final song = results[index];
        return GlassSearchResultTile(
          song: song,
          onTap: () => _playSong(song, results, index),
          onMoreTap: () => _showSongOptions(song),
        );
      },
    );
  }

  Widget _buildDiscoverContent(search_ctrl.SearchController searchController) {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trending music
          if (searchController.hasTrending)
            GlassTrendingSection(
              trendingSongs: searchController.trendingMusic,
              onSongTap: (song) =>
                  _playSong(song, searchController.trendingMusic, 0),
              onMoreTap: (song) => _showSongOptions(song),
            ),

          const SizedBox(height: 16),

          // Popular genres
          GlassGenreChips(
            title: 'Popular Genres',
            genres: searchController.popularGenres,
            selectedGenre: searchController.selectedGenre,
            onGenreSelected: (genre) {
              searchController.searchByGenre(genre);
            },
          ),

          const SizedBox(height: 16),

          // Mood-based searches
          GlassGenreChips(
            title: 'Search by Mood',
            genres: searchController.moodSearches,
            selectedGenre: searchController.selectedMood,
            onGenreSelected: (mood) {
              _searchController.text = mood;
              searchController.searchByMood(mood);
            },
          ),

          const SizedBox(height: 16),

          // Search history
          GlassSearchHistory(
            searchHistory: searchController.searchHistory,
            onHistoryTap: (query) {
              _searchController.text = query;
              searchController.searchMusic(query, immediate: true);
            },
            onHistoryRemove: searchController.removeFromSearchHistory,
            onClearAll: searchController.clearSearchHistory,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySearchState() {
    return GlassLibraryEmptyState(
      icon: Icons.search_off,
      title: 'No results found',
      subtitle: 'Try searching with different keywords',
      actionText: 'Browse Trending',
      onActionTap: () {
        _searchController.clear();
        final searchController = Provider.of<search_ctrl.SearchController>(
          context,
          listen: false,
        );
        searchController.clearSearch();
      },
    );
  }

  Widget _buildErrorState(String error) {
    return GlassLibraryEmptyState(
      icon: Icons.error_outline,
      title: 'Search Error',
      subtitle: error,
      actionText: 'Retry',
      onActionTap: () {
        final searchController = Provider.of<search_ctrl.SearchController>(
          context,
          listen: false,
        );
        searchController.refresh();
      },
    );
  }

  void _playSong(Song song, List<Song> songs, int index) {
    final audioController = Provider.of<AudioController>(
      context,
      listen: false,
    );
    audioController.playSong(song);

    // Add song to library if not already there
    final libraryController = Provider.of<LibraryController>(
      context,
      listen: false,
    );
    libraryController.addSong(song);
  }

  void _showSongOptions(Song song) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSongOptionsSheet(song),
    );
  }

  Widget _buildSongOptionsSheet(Song song) {
    return GlassContainer(
      margin: const EdgeInsets.all(16),
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSheetHeader(song.title, song.artist),
          _buildSheetOption(Icons.play_arrow, 'Play now', () {
            Navigator.pop(context);
            _playSong(song, [song], 0);
          }),
          _buildSheetOption(Icons.queue, 'Add to queue', () {
            Navigator.pop(context);
            final audioController = Provider.of<AudioController>(
              context,
              listen: false,
            );
            audioController.addToQueue(song);
          }),
          _buildSheetOption(Icons.playlist_add, 'Add to playlist', () {
            Navigator.pop(context);
            // TODO: Show playlist selection
          }),
          _buildSheetOption(Icons.library_add, 'Add to library', () {
            Navigator.pop(context);
            final libraryController = Provider.of<LibraryController>(
              context,
              listen: false,
            );
            libraryController.addSong(song);
          }),
          _buildSheetOption(Icons.favorite_border, 'Add to favorites', () {
            Navigator.pop(context);
            final libraryController = Provider.of<LibraryController>(
              context,
              listen: false,
            );
            libraryController.addSong(song.copyWith(isFavorite: true));
          }),
          _buildSheetOption(Icons.share, 'Share', () {
            Navigator.pop(context);
            // TODO: Share functionality
          }),
          _buildSheetOption(Icons.explore, 'Find similar', () {
            Navigator.pop(context);
            _findSimilarSongs(song);
          }),
        ],
      ),
    );
  }

  Widget _buildSheetHeader(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSheetOption(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      onTap: onTap,
    );
  }

  void _findSimilarSongs(Song song) async {
    final searchController = Provider.of<search_ctrl.SearchController>(
      context,
      listen: false,
    );
    final similarSongs = await searchController.searchSimilar(
      song.title,
      song.artist,
    );

    if (similarSongs.isNotEmpty) {
      _searchController.text = 'Similar to ${song.title}';
      // Update search results with similar songs
      // This would require updating the search controller to handle custom results
    }
  }
}
