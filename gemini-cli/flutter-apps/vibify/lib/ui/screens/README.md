# Screens Directory

This directory contains all app screens and pages.

## Planned Screens:

- `splash_screen.dart` - App loading screen with animations
- `onboarding_screen.dart` - First-time user onboarding flow
- `home_screen.dart` - Main tabbed interface (All Songs, Favorites, Downloads, Playlists)
- `player_screen.dart` - Full-screen music player with glass UI
- `search_screen.dart` - YouTube search interface
- `playlist_screen.dart` - Individual playlist view
- `settings_screen.dart` - App settings and configuration
- `downloads_screen.dart` - Download management interface

## Navigation Flow:
Splash → Onboarding (first time) → Home → Player/Search/Settings

All screens will:
- Use liquid glass theme consistently
- Handle responsive design properly
- Implement proper loading and error states
- Follow Material Design guidelines
