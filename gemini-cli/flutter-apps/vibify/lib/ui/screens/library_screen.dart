import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_app_bar.dart';
import '../widgets/glass_library_widgets.dart';
import '../../controllers/library_controller.dart';
import '../../controllers/audio_controller.dart';
import '../../models/song.dart';
import '../../models/playlist.dart';

/// Main library screen with tabs for different content types
class LibraryScreen extends StatefulWidget {
  const LibraryScreen({super.key});

  @override
  State<LibraryScreen> createState() => _LibraryScreenState();
}

class _LibraryScreenState extends State<LibraryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom app bar with search
              _buildAppBar(),
              
              // Tab bar
              _buildTabBar(),
              
              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSongsTab(),
                    _buildPlaylistsTab(),
                    _buildArtistsTab(),
                    _buildAlbumsTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Column(
      children: [
        GlassAppBar(
          title: 'Your Library',
          actions: [
            GlassAppBarAction(
              icon: Icons.sort,
              onPressed: _showSortOptions,
              tooltip: 'Sort',
            ),
            GlassAppBarAction(
              icon: Icons.more_vert,
              onPressed: _showLibraryMenu,
              tooltip: 'More',
            ),
          ],
        ),
        
        // Search bar
        Consumer<LibraryController>(
          builder: (context, libraryController, child) {
            return GlassLibrarySearchBar(
              controller: _searchController,
              hintText: 'Search your library...',
              onChanged: libraryController.setSearchQuery,
              onClear: () => libraryController.setSearchQuery(''),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: GlassContainer(
        padding: const EdgeInsets.all(4),
        borderRadius: BorderRadius.circular(25),
        child: TabBar(
          controller: _tabController,
          indicator: BoxDecoration(
            gradient: LinearGradient(
              colors: VibifyColors.purpleGradient,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
          labelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          tabs: const [
            Tab(text: 'Songs'),
            Tab(text: 'Playlists'),
            Tab(text: 'Artists'),
            Tab(text: 'Albums'),
          ],
        ),
      ),
    );
  }

  Widget _buildSongsTab() {
    return Consumer2<LibraryController, AudioController>(
      builder: (context, libraryController, audioController, child) {
        if (libraryController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final songs = libraryController.filteredSongs;
        final currentSong = audioController.currentSong;

        if (songs.isEmpty) {
          return GlassLibraryEmptyState(
            icon: Icons.music_note_outlined,
            title: 'No songs in your library',
            subtitle: 'Add songs by downloading or importing music',
            actionText: 'Browse Music',
            onActionTap: () {
              // TODO: Navigate to search/browse
            },
          );
        }

        return Column(
          children: [
            // Filter chips
            _buildFilterChips(libraryController),
            
            // Songs list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.only(bottom: 100), // Space for mini player
                itemCount: songs.length,
                itemBuilder: (context, index) {
                  final song = songs[index];
                  final isPlaying = currentSong?.id == song.id;
                  
                  return GlassSongTile(
                    song: song,
                    showIndex: true,
                    index: index,
                    isPlaying: isPlaying,
                    onTap: () => _playSong(song, songs, index),
                    onMoreTap: () => _showSongOptions(song),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPlaylistsTab() {
    return Consumer<LibraryController>(
      builder: (context, libraryController, child) {
        if (libraryController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final playlists = libraryController.playlists;
        final systemPlaylists = [
          libraryController.favoritesPlaylist,
          libraryController.downloadsPlaylist,
          libraryController.recentPlaylist,
        ];

        return ListView(
          padding: const EdgeInsets.only(bottom: 100),
          children: [
            // Quick access playlists
            GlassLibraryHeader(
              title: 'Quick Access',
              subtitle: 'Your favorite collections',
            ),
            ...systemPlaylists.map((playlist) {
              final songCount = _getSystemPlaylistSongCount(playlist, libraryController);
              return GlassPlaylistTile(
                playlist: playlist,
                songCount: songCount,
                onTap: () => _openPlaylist(playlist, libraryController),
              );
            }),
            
            const SizedBox(height: 16),
            
            // User playlists
            GlassLibraryHeader(
              title: 'Your Playlists',
              subtitle: '${playlists.length} playlists',
              onSeeAllTap: playlists.length > 5 ? () {} : null,
            ),
            
            if (playlists.isEmpty)
              GlassLibraryEmptyState(
                icon: Icons.queue_music_outlined,
                title: 'No playlists yet',
                subtitle: 'Create playlists to organize your music',
                actionText: 'Create Playlist',
                onActionTap: _createPlaylist,
              )
            else
              ...playlists.take(5).map((playlist) {
                return GlassPlaylistTile(
                  playlist: playlist,
                  songCount: playlist.songCount,
                  subtitle: playlist.formattedDateCreated,
                  onTap: () => _openPlaylist(playlist, libraryController),
                  onMoreTap: () => _showPlaylistOptions(playlist),
                );
              }),
          ],
        );
      },
    );
  }

  Widget _buildArtistsTab() {
    return Consumer<LibraryController>(
      builder: (context, libraryController, child) {
        if (libraryController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final artists = libraryController.artists;

        if (artists.isEmpty) {
          return GlassLibraryEmptyState(
            icon: Icons.person_outline,
            title: 'No artists in your library',
            subtitle: 'Artists will appear as you add music',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: artists.length,
          itemBuilder: (context, index) {
            final artist = artists[index];
            return GlassPlaylistTile(
              playlist: Playlist(
                id: artist.id,
                name: artist.name,
                description: '${artist.songCount} songs',
              ),
              songCount: artist.songCount,
              subtitle: '${artist.songCount} songs',
              onTap: () => _openArtist(artist),
            );
          },
        );
      },
    );
  }

  Widget _buildAlbumsTab() {
    return Consumer<LibraryController>(
      builder: (context, libraryController, child) {
        if (libraryController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final albums = libraryController.albums;

        if (albums.isEmpty) {
          return GlassLibraryEmptyState(
            icon: Icons.album_outlined,
            title: 'No albums in your library',
            subtitle: 'Albums will appear as you add music',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: albums.length,
          itemBuilder: (context, index) {
            final album = albums[index];
            return GlassPlaylistTile(
              playlist: Playlist(
                id: album.id,
                name: album.title,
                description: '${album.artistName} • ${album.songCount} songs',
              ),
              songCount: album.songCount,
              subtitle: '${album.artistName} • ${album.songCount} songs',
              onTap: () => _openAlbum(album),
            );
          },
        );
      },
    );
  }

  Widget _buildFilterChips(LibraryController libraryController) {
    final filters = ['All', 'Downloaded', 'Favorites', 'Recent'];
    final currentFilter = _getFilterDisplayName(libraryController.currentFilter);

    return GlassFilterChips(
      filters: filters,
      selectedFilter: currentFilter,
      onFilterChanged: (filter) {
        final filterOption = _getFilterOption(filter);
        libraryController.setFilter(filterOption);
      },
    );
  }

  String _getFilterDisplayName(FilterOption filter) {
    switch (filter) {
      case FilterOption.all:
        return 'All';
      case FilterOption.downloaded:
        return 'Downloaded';
      case FilterOption.favorites:
        return 'Favorites';
      case FilterOption.recent:
        return 'Recent';
    }
  }

  FilterOption _getFilterOption(String displayName) {
    switch (displayName) {
      case 'Downloaded':
        return FilterOption.downloaded;
      case 'Favorites':
        return FilterOption.favorites;
      case 'Recent':
        return FilterOption.recent;
      default:
        return FilterOption.all;
    }
  }

  int _getSystemPlaylistSongCount(Playlist playlist, LibraryController libraryController) {
    switch (playlist.id) {
      case 'favorites':
        return libraryController.favoriteSongs.length;
      case 'downloads':
        return libraryController.downloadedSongs.length;
      case 'recent':
        return libraryController.recentSongs.length;
      default:
        return 0;
    }
  }

  void _playSong(Song song, List<Song> songs, int index) {
    final audioController = Provider.of<AudioController>(context, listen: false);
    audioController.playPlaylist(Playlist(id: 'library', name: 'Library'), songs, startIndex: index);
  }

  void _openPlaylist(Playlist playlist, LibraryController libraryController) {
    final songs = libraryController.getPlaylistSongs(playlist);
    // TODO: Navigate to playlist detail screen
    debugPrint('Opening playlist: ${playlist.name} with ${songs.length} songs');
  }

  void _openArtist(artist) {
    // TODO: Navigate to artist detail screen
    debugPrint('Opening artist: ${artist.name}');
  }

  void _openAlbum(album) {
    // TODO: Navigate to album detail screen
    debugPrint('Opening album: ${album.title}');
  }

  void _showSongOptions(Song song) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSongOptionsSheet(song),
    );
  }

  void _showPlaylistOptions(Playlist playlist) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPlaylistOptionsSheet(playlist),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSortOptionsSheet(),
    );
  }

  void _showLibraryMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildLibraryMenuSheet(),
    );
  }

  void _createPlaylist() {
    // TODO: Show create playlist dialog
    debugPrint('Create playlist');
  }

  Widget _buildSongOptionsSheet(Song song) {
    return GlassContainer(
      margin: const EdgeInsets.all(16),
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSheetHeader(song.title, song.artist),
          _buildSheetOption(Icons.play_arrow, 'Play', () {}),
          _buildSheetOption(Icons.queue, 'Add to queue', () {}),
          _buildSheetOption(Icons.playlist_add, 'Add to playlist', () {}),
          _buildSheetOption(
            song.isFavorite ? Icons.favorite : Icons.favorite_border,
            song.isFavorite ? 'Remove from favorites' : 'Add to favorites',
            () => _toggleFavorite(song),
          ),
          _buildSheetOption(Icons.share, 'Share', () {}),
          _buildSheetOption(Icons.info, 'Song info', () {}),
        ],
      ),
    );
  }

  Widget _buildPlaylistOptionsSheet(Playlist playlist) {
    return GlassContainer(
      margin: const EdgeInsets.all(16),
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSheetHeader(playlist.name, '${playlist.songCount} songs'),
          _buildSheetOption(Icons.play_arrow, 'Play', () {}),
          _buildSheetOption(Icons.shuffle, 'Shuffle', () {}),
          _buildSheetOption(Icons.edit, 'Edit playlist', () {}),
          _buildSheetOption(Icons.share, 'Share', () {}),
          _buildSheetOption(Icons.delete, 'Delete playlist', () {}),
        ],
      ),
    );
  }

  Widget _buildSortOptionsSheet() {
    return Consumer<LibraryController>(
      builder: (context, libraryController, child) {
        return GlassContainer(
          margin: const EdgeInsets.all(16),
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSheetHeader('Sort by', ''),
              _buildSortOption('Title', SortOption.title, libraryController),
              _buildSortOption('Artist', SortOption.artist, libraryController),
              _buildSortOption('Album', SortOption.album, libraryController),
              _buildSortOption('Date added', SortOption.dateAdded, libraryController),
              _buildSortOption('Duration', SortOption.duration, libraryController),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLibraryMenuSheet() {
    return GlassContainer(
      margin: const EdgeInsets.all(16),
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSheetHeader('Library', ''),
          _buildSheetOption(Icons.refresh, 'Refresh library', () {}),
          _buildSheetOption(Icons.import_export, 'Import music', () {}),
          _buildSheetOption(Icons.settings, 'Library settings', () {}),
        ],
      ),
    );
  }

  Widget _buildSheetHeader(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          if (subtitle.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSheetOption(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: Text(title, style: const TextStyle(color: Colors.white)),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }

  Widget _buildSortOption(String title, SortOption option, LibraryController controller) {
    final isSelected = controller.currentSort == option;
    return ListTile(
      leading: Icon(
        isSelected ? Icons.check : Icons.sort,
        color: isSelected ? VibifyColors.primaryPurple : Colors.white,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? VibifyColors.primaryPurple : Colors.white,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing: isSelected
          ? Icon(
              controller.sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
              color: VibifyColors.primaryPurple,
              size: 16,
            )
          : null,
      onTap: () {
        Navigator.pop(context);
        if (isSelected) {
          controller.toggleSortOrder();
        } else {
          controller.setSortOption(option);
        }
      },
    );
  }

  void _toggleFavorite(Song song) {
    final libraryController = Provider.of<LibraryController>(context, listen: false);
    libraryController.toggleFavorite(song.id);
  }
}
