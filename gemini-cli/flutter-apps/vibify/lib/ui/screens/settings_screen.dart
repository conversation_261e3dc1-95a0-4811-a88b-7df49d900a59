import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/colors.dart';
import '../theme/glass_container.dart';
import '../widgets/glass_app_bar.dart';
import '../widgets/glass_settings_widgets.dart';
import '../../controllers/settings_controller.dart';
import '../../services/settings_service.dart';
import '../screens/downloads_screen.dart';

/// Main settings screen with categorized options
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final brightness = theme.brightness;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: brightness == Brightness.dark
                ? VibifyColors.darkGradient
                : VibifyColors.lightGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App bar
              GlassAppBar(
                title: 'Settings',
                actions: [
                  GlassAppBarAction(
                    icon: Icons.restore,
                    onPressed: _showResetDialog,
                    tooltip: 'Reset to defaults',
                  ),
                ],
              ),

              // Settings content
              Expanded(
                child: Consumer<SettingsController>(
                  builder: (context, settingsController, child) {
                    if (settingsController.isLoading) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    return ListView(
                      controller: _scrollController,
                      padding: const EdgeInsets.only(bottom: 100),
                      children: [
                        // Audio settings
                        _buildAudioSettings(settingsController),

                        // Download settings
                        _buildDownloadSettings(settingsController),

                        // Appearance settings
                        _buildAppearanceSettings(settingsController),

                        // Playback settings
                        _buildPlaybackSettings(settingsController),

                        // Library settings
                        _buildLibrarySettings(settingsController),

                        // Privacy settings
                        _buildPrivacySettings(settingsController),

                        // Advanced settings
                        _buildAdvancedSettings(settingsController),

                        // About section
                        _buildAboutSection(settingsController),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAudioSettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Audio',
          subtitle: 'Sound quality and audio preferences',
          icon: Icons.volume_up,
        ),

        GlassSettingsSelector<AudioQuality>(
          title: 'Audio Quality',
          subtitle: 'Streaming audio quality',
          icon: Icons.high_quality,
          value: controller.audioQuality,
          options: AudioQuality.values,
          optionFormatter: (quality) => quality.name.toUpperCase(),
          onChanged: controller.setAudioQuality,
        ),

        GlassSettingsSlider(
          title: 'Volume',
          subtitle: 'Default playback volume',
          icon: Icons.volume_up,
          value: controller.volume,
          min: 0.0,
          max: 1.0,
          divisions: 20,
          valueFormatter: (value) => '${(value * 100).round()}%',
          onChanged: controller.setVolume,
        ),

        GlassSettingsSlider(
          title: 'Crossfade Duration',
          subtitle: 'Smooth transition between songs',
          icon: Icons.shuffle,
          value: controller.getSetting('crossfade_duration', defaultValue: 3.0),
          min: 0.0,
          max: 10.0,
          divisions: 20,
          valueFormatter: (value) => '${value.toStringAsFixed(1)}s',
          onChanged: controller.setCrossfadeDuration,
        ),

        GlassSettingsToggle(
          title: 'Normalize Volume',
          subtitle: 'Automatically adjust volume levels',
          icon: Icons.equalizer,
          value: controller.getSetting('normalize_volume', defaultValue: true),
          onChanged: (value) =>
              controller.setSetting('normalize_volume', value),
        ),

        GlassSettingsToggle(
          title: 'Gapless Playback',
          subtitle: 'Seamless playback between tracks',
          icon: Icons.skip_next,
          value: controller.getSetting('gapless_playback', defaultValue: true),
          onChanged: (value) =>
              controller.setSetting('gapless_playback', value),
        ),
      ],
    );
  }

  Widget _buildDownloadSettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Downloads',
          subtitle: 'Offline music and download preferences',
          icon: Icons.download,
        ),

        GlassSettingsSelector<DownloadQuality>(
          title: 'Download Quality',
          subtitle: 'Quality for offline downloads',
          icon: Icons.high_quality,
          value: controller.downloadQuality,
          options: DownloadQuality.values,
          optionFormatter: (quality) => quality.name.toUpperCase(),
          onChanged: controller.setDownloadQuality,
        ),

        GlassSettingsToggle(
          title: 'Download over Wi-Fi only',
          subtitle: 'Prevent mobile data usage',
          icon: Icons.wifi,
          value: controller.downloadOverWifiOnly,
          onChanged: (value) =>
              controller.setSetting('download_over_wifi_only', value),
        ),

        GlassSettingsToggle(
          title: 'Auto-download favorites',
          subtitle: 'Automatically download liked songs',
          icon: Icons.favorite,
          value: controller.getSetting(
            'auto_download_favorites',
            defaultValue: false,
          ),
          onChanged: (value) =>
              controller.setSetting('auto_download_favorites', value),
        ),

        GlassSettingsSlider(
          title: 'Concurrent Downloads',
          subtitle: 'Maximum simultaneous downloads',
          icon: Icons.download_for_offline,
          value: controller
              .getSetting('max_concurrent_downloads', defaultValue: 3)
              .toDouble(),
          min: 1.0,
          max: 10.0,
          divisions: 9,
          valueFormatter: (value) => '${value.round()} downloads',
          onChanged: (value) =>
              controller.setMaxConcurrentDownloads(value.round()),
        ),

        GlassSettingsAction(
          title: 'Manage Downloads',
          subtitle: 'View and manage your downloads',
          icon: Icons.download_for_offline,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const DownloadsScreen()),
            );
          },
        ),
      ],
    );
  }

  Widget _buildAppearanceSettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Appearance',
          subtitle: 'Theme and visual preferences',
          icon: Icons.palette,
        ),

        GlassSettingsSelector<AppThemeMode>(
          title: 'Theme',
          subtitle: 'App appearance mode',
          icon: Icons.brightness_6,
          value: controller.themeMode,
          options: AppThemeMode.values,
          optionFormatter: (mode) =>
              mode.name.replaceAll('_', ' ').toUpperCase(),
          onChanged: controller.setThemeMode,
        ),

        GlassSettingsSlider(
          title: 'Glass Intensity',
          subtitle: 'Transparency and blur effect',
          icon: Icons.blur_on,
          value: controller.glassIntensity,
          min: 0.0,
          max: 1.0,
          divisions: 20,
          valueFormatter: (value) => '${(value * 100).round()}%',
          onChanged: controller.setGlassIntensity,
        ),

        GlassSettingsToggle(
          title: 'Show Album Art',
          subtitle: 'Display album artwork',
          icon: Icons.image,
          value: controller.getSetting('show_album_art', defaultValue: true),
          onChanged: (value) => controller.setSetting('show_album_art', value),
        ),

        GlassSettingsToggle(
          title: 'Animated Backgrounds',
          subtitle: 'Dynamic background effects',
          icon: Icons.animation,
          value: controller.getSetting(
            'animated_backgrounds',
            defaultValue: true,
          ),
          onChanged: (value) =>
              controller.setSetting('animated_backgrounds', value),
        ),

        GlassSettingsToggle(
          title: 'Reduce Motion',
          subtitle: 'Minimize animations for accessibility',
          icon: Icons.accessibility,
          value: controller.getSetting('reduce_motion', defaultValue: false),
          onChanged: (value) => controller.setSetting('reduce_motion', value),
        ),
      ],
    );
  }

  Widget _buildPlaybackSettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Playback',
          subtitle: 'Music playback behavior',
          icon: Icons.play_circle,
        ),

        GlassSettingsToggle(
          title: 'Auto-play Next',
          subtitle: 'Continue playing after current song',
          icon: Icons.skip_next,
          value: controller.getSetting('auto_play_next', defaultValue: true),
          onChanged: (value) => controller.setSetting('auto_play_next', value),
        ),

        GlassSettingsToggle(
          title: 'Skip Silence',
          subtitle: 'Automatically skip silent parts',
          icon: Icons.fast_forward,
          value: controller.getSetting('skip_silence', defaultValue: false),
          onChanged: (value) => controller.setSetting('skip_silence', value),
        ),

        GlassSettingsToggle(
          title: 'Remember Playback Position',
          subtitle: 'Resume from where you left off',
          icon: Icons.restore,
          value: controller.getSetting(
            'remember_playback_position',
            defaultValue: true,
          ),
          onChanged: (value) =>
              controller.setSetting('remember_playback_position', value),
        ),
      ],
    );
  }

  Widget _buildLibrarySettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Library',
          subtitle: 'Music library organization',
          icon: Icons.library_music,
        ),

        GlassSettingsToggle(
          title: 'Auto-add to Library',
          subtitle: 'Automatically add played songs',
          icon: Icons.library_add,
          value: controller.getSetting(
            'auto_add_to_library',
            defaultValue: true,
          ),
          onChanged: (value) =>
              controller.setSetting('auto_add_to_library', value),
        ),

        GlassSettingsToggle(
          title: 'Show Explicit Content',
          subtitle: 'Display explicit music content',
          icon: Icons.explicit,
          value: controller.getSetting(
            'show_explicit_content',
            defaultValue: true,
          ),
          onChanged: (value) =>
              controller.setSetting('show_explicit_content', value),
        ),

        GlassSettingsToggle(
          title: 'Group by Album Artist',
          subtitle: 'Organize albums by main artist',
          icon: Icons.group,
          value: controller.getSetting(
            'group_by_album_artist',
            defaultValue: false,
          ),
          onChanged: (value) =>
              controller.setSetting('group_by_album_artist', value),
        ),
      ],
    );
  }

  Widget _buildPrivacySettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Privacy',
          subtitle: 'Data and privacy preferences',
          icon: Icons.privacy_tip,
        ),

        GlassSettingsToggle(
          title: 'Analytics',
          subtitle: 'Help improve the app with usage data',
          icon: Icons.analytics,
          value: controller.analyticsEnabled,
          onChanged: (value) =>
              controller.setSetting('analytics_enabled', value),
        ),

        GlassSettingsToggle(
          title: 'Crash Reporting',
          subtitle: 'Send crash reports to developers',
          icon: Icons.bug_report,
          value: controller.getSetting('crash_reporting', defaultValue: true),
          onChanged: (value) => controller.setSetting('crash_reporting', value),
        ),

        GlassSettingsToggle(
          title: 'Personalized Recommendations',
          subtitle: 'Use listening history for suggestions',
          icon: Icons.recommend,
          value: controller.getSetting(
            'personalized_recommendations',
            defaultValue: true,
          ),
          onChanged: (value) =>
              controller.setSetting('personalized_recommendations', value),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'Advanced',
          subtitle: 'Developer and advanced options',
          icon: Icons.settings_applications,
        ),

        GlassSettingsSlider(
          title: 'Cache Size',
          subtitle: 'Storage for temporary files',
          icon: Icons.storage,
          value: controller
              .getSetting('cache_size_mb', defaultValue: 500)
              .toDouble(),
          min: 100.0,
          max: 2000.0,
          divisions: 19,
          valueFormatter: (value) => '${value.round()}MB',
          onChanged: (value) => controller.setCacheSize(value.round()),
        ),

        GlassSettingsToggle(
          title: 'Developer Mode',
          subtitle: 'Enable advanced debugging features',
          icon: Icons.developer_mode,
          value: controller.developerMode,
          onChanged: (value) => controller.setSetting('developer_mode', value),
        ),

        if (controller.developerMode)
          GlassSettingsToggle(
            title: 'Debug Logging',
            subtitle: 'Verbose logging for troubleshooting',
            icon: Icons.terminal,
            value: controller.getSetting('debug_logging', defaultValue: false),
            onChanged: (value) => controller.setSetting('debug_logging', value),
          ),

        GlassSettingsAction(
          title: 'Clear Cache',
          subtitle: 'Free up storage space',
          icon: Icons.clear_all,
          onTap: _clearCache,
        ),

        GlassSettingsAction(
          title: 'Export Settings',
          subtitle: 'Backup your preferences',
          icon: Icons.file_upload,
          onTap: _exportSettings,
        ),

        GlassSettingsAction(
          title: 'Import Settings',
          subtitle: 'Restore from backup',
          icon: Icons.file_download,
          onTap: _importSettings,
        ),
      ],
    );
  }

  Widget _buildAboutSection(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GlassSettingsHeader(
          title: 'About',
          subtitle: 'App information and support',
          icon: Icons.info,
        ),

        GlassSettingsInfo(
          title: 'Version',
          value: '1.0.0',
          icon: Icons.info_outline,
        ),

        GlassSettingsAction(
          title: 'Privacy Policy',
          icon: Icons.policy,
          onTap: _openPrivacyPolicy,
        ),

        GlassSettingsAction(
          title: 'Terms of Service',
          icon: Icons.description,
          onTap: _openTermsOfService,
        ),

        GlassSettingsAction(
          title: 'Open Source Licenses',
          icon: Icons.code,
          onTap: _openLicenses,
        ),

        GlassSettingsAction(
          title: 'Contact Support',
          icon: Icons.support,
          onTap: _contactSupport,
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.transparent,
        content: GlassContainer.dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Reset Settings',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'This will reset all settings to their default values. This action cannot be undone.',
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: GlassContainer.button(
                      onTap: () => Navigator.pop(context),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: GlassContainer.button(
                      onTap: () {
                        Navigator.pop(context);
                        final controller = Provider.of<SettingsController>(
                          context,
                          listen: false,
                        );
                        controller.resetToDefaults();
                      },
                      child: const Text(
                        'Reset',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _clearCache() {
    // TODO: Implement cache clearing
    debugPrint('Clear cache');
  }

  void _exportSettings() {
    // TODO: Implement settings export
    debugPrint('Export settings');
  }

  void _importSettings() {
    // TODO: Implement settings import
    debugPrint('Import settings');
  }

  void _openPrivacyPolicy() {
    // TODO: Open privacy policy
    debugPrint('Open privacy policy');
  }

  void _openTermsOfService() {
    // TODO: Open terms of service
    debugPrint('Open terms of service');
  }

  void _openLicenses() {
    showLicensePage(context: context);
  }

  void _contactSupport() {
    // TODO: Open support contact
    debugPrint('Contact support');
  }
}
