import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Custom page transitions for Vibify
class VibifyPageTransitions {
  /// Glass slide transition with blur effect
  static PageRouteBuilder<T> glassSlide<T>({
    required Widget page,
    RouteSettings? settings,
    Duration duration = const Duration(milliseconds: 400),
    bool slideFromRight = true,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(
          begin: slideFromRight ? begin : const Offset(-1.0, 0.0),
          end: end,
        ).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        return SlideTransition(
          position: offsetAnimation,
          child: FadeTransition(opacity: fadeAnimation, child: child),
        );
      },
    );
  }

  /// Scale transition with glass effect
  static PageRouteBuilder<T> glassScale<T>({
    required Widget page,
    RouteSettings? settings,
    Duration duration = const Duration(milliseconds: 350),
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeInOutBack;

        var scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return ScaleTransition(
          scale: scaleAnimation,
          child: FadeTransition(opacity: fadeAnimation, child: child),
        );
      },
    );
  }

  /// Rotation transition for special screens
  static PageRouteBuilder<T> glassRotation<T>({
    required Widget page,
    RouteSettings? settings,
    Duration duration = const Duration(milliseconds: 500),
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeInOutCubic;

        var rotationAnimation = Tween<double>(
          begin: 0.1,
          end: 0.0,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        var scaleAnimation = Tween<double>(
          begin: 0.9,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return Transform.rotate(
          angle: rotationAnimation.value,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: FadeTransition(opacity: fadeAnimation, child: child),
          ),
        );
      },
    );
  }

  /// Shared element transition for music player
  static PageRouteBuilder<T> sharedElement<T>({
    required Widget page,
    required String heroTag,
    RouteSettings? settings,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeInOutCubic;

        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 1.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(opacity: fadeAnimation, child: child),
        );
      },
    );
  }

  /// Bottom sheet style transition
  static PageRouteBuilder<T> bottomSheet<T>({
    required Widget page,
    RouteSettings? settings,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeOutCubic;

        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 1.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(opacity: fadeAnimation, child: child),
        );
      },
    );
  }
}

/// Custom route for glass-themed navigation
class GlassRoute<T> extends PageRoute<T> {
  final Widget child;
  final Duration _transitionDuration;
  final String transitionType;

  GlassRoute({
    required this.child,
    Duration transitionDuration = const Duration(milliseconds: 400),
    this.transitionType = 'slide',
    super.settings,
  }) : _transitionDuration = transitionDuration;

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => this._transitionDuration;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return child;
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    switch (transitionType) {
      case 'scale':
        return _buildScaleTransition(animation, child);
      case 'rotation':
        return _buildRotationTransition(animation, child);
      case 'fade':
        return _buildFadeTransition(animation, child);
      default:
        return _buildSlideTransition(animation, child);
    }
  }

  Widget _buildSlideTransition(Animation<double> animation, Widget child) {
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    const curve = Curves.easeInOutCubic;

    var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
    var offsetAnimation = animation.drive(tween);

    return SlideTransition(position: offsetAnimation, child: child);
  }

  Widget _buildScaleTransition(Animation<double> animation, Widget child) {
    const curve = Curves.easeInOutBack;

    var scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animation, curve: curve));

    return ScaleTransition(scale: scaleAnimation, child: child);
  }

  Widget _buildRotationTransition(Animation<double> animation, Widget child) {
    const curve = Curves.easeInOutCubic;

    var rotationAnimation = Tween<double>(
      begin: 0.1,
      end: 0.0,
    ).animate(CurvedAnimation(parent: animation, curve: curve));

    var scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(parent: animation, curve: curve));

    return Transform.rotate(
      angle: rotationAnimation.value,
      child: ScaleTransition(scale: scaleAnimation, child: child),
    );
  }

  Widget _buildFadeTransition(Animation<double> animation, Widget child) {
    return FadeTransition(opacity: animation, child: child);
  }
}

/// Navigation helper with custom transitions
class VibifyNavigator {
  /// Navigate with glass slide transition
  static Future<T?> slideToPage<T>(
    BuildContext context,
    Widget page, {
    bool slideFromRight = true,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return Navigator.push<T>(
      context,
      VibifyPageTransitions.glassSlide<T>(
        page: page,
        slideFromRight: slideFromRight,
        duration: duration,
      ),
    );
  }

  /// Navigate with scale transition
  static Future<T?> scaleToPage<T>(
    BuildContext context,
    Widget page, {
    Duration duration = const Duration(milliseconds: 350),
  }) {
    return Navigator.push<T>(
      context,
      VibifyPageTransitions.glassScale<T>(page: page, duration: duration),
    );
  }

  /// Navigate with rotation transition
  static Future<T?> rotateToPage<T>(
    BuildContext context,
    Widget page, {
    Duration duration = const Duration(milliseconds: 500),
  }) {
    return Navigator.push<T>(
      context,
      VibifyPageTransitions.glassRotation<T>(page: page, duration: duration),
    );
  }

  /// Navigate with bottom sheet style
  static Future<T?> slideUpToPage<T>(
    BuildContext context,
    Widget page, {
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return Navigator.push<T>(
      context,
      VibifyPageTransitions.bottomSheet<T>(page: page, duration: duration),
    );
  }

  /// Navigate with shared element transition
  static Future<T?> sharedElementToPage<T>(
    BuildContext context,
    Widget page,
    String heroTag, {
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return Navigator.push<T>(
      context,
      VibifyPageTransitions.sharedElement<T>(
        page: page,
        heroTag: heroTag,
        duration: duration,
      ),
    );
  }
}
