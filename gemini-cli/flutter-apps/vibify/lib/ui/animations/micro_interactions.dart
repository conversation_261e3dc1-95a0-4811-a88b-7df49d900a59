import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Micro-interactions for enhanced user feedback
class VibifyMicroInteractions {
  
  /// Haptic feedback patterns
  static void lightImpact() {
    HapticFeedback.lightImpact();
  }
  
  static void mediumImpact() {
    HapticFeedback.mediumImpact();
  }
  
  static void heavyImpact() {
    HapticFeedback.heavyImpact();
  }
  
  static void selectionClick() {
    HapticFeedback.selectionClick();
  }
}

/// Animated heart for favorite button
class AnimatedHeart extends StatefulWidget {
  final bool isLiked;
  final ValueChanged<bool>? onChanged;
  final double size;
  final Color likedColor;
  final Color unlikedColor;

  const AnimatedHeart({
    super.key,
    required this.isLiked,
    this.onChanged,
    this.size = 24,
    this.likedColor = Colors.red,
    this.unlikedColor = Colors.grey,
  });

  @override
  State<AnimatedHeart> createState() => _AnimatedHeartState();
}

class _AnimatedHeartState extends State<AnimatedHeart>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.3)
        .animate(CurvedAnimation(
          parent: _controller,
          curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
        ));

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.1)
        .animate(CurvedAnimation(
          parent: _controller,
          curve: const Interval(0.0, 0.3, curve: Curves.easeInOut),
        ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap() {
    if (widget.isLiked) {
      _controller.reverse();
    } else {
      _controller.forward().then((_) => _controller.reverse());
    }
    
    VibifyMicroInteractions.lightImpact();
    widget.onChanged?.call(!widget.isLiked);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Icon(
                widget.isLiked ? Icons.favorite : Icons.favorite_border,
                size: widget.size,
                color: widget.isLiked ? widget.likedColor : widget.unlikedColor,
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Animated play/pause button
class AnimatedPlayPause extends StatefulWidget {
  final bool isPlaying;
  final VoidCallback? onPressed;
  final double size;
  final Color color;

  const AnimatedPlayPause({
    super.key,
    required this.isPlaying,
    this.onPressed,
    this.size = 48,
    this.color = Colors.white,
  });

  @override
  State<AnimatedPlayPause> createState() => _AnimatedPlayPauseState();
}

class _AnimatedPlayPauseState extends State<AnimatedPlayPause>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTap() {
    _controller.forward().then((_) => _controller.reverse());
    VibifyMicroInteractions.mediumImpact();
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.color.withValues(alpha: 0.2),
                border: Border.all(color: widget.color.withValues(alpha: 0.3)),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (child, animation) {
                  return RotationTransition(
                    turns: animation,
                    child: child,
                  );
                },
                child: Icon(
                  widget.isPlaying ? Icons.pause : Icons.play_arrow,
                  key: ValueKey(widget.isPlaying),
                  size: widget.size * 0.6,
                  color: widget.color,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Animated volume slider with visual feedback
class AnimatedVolumeSlider extends StatefulWidget {
  final double value;
  final ValueChanged<double>? onChanged;
  final Color activeColor;
  final Color inactiveColor;

  const AnimatedVolumeSlider({
    super.key,
    required this.value,
    this.onChanged,
    this.activeColor = const Color(0xFF8B5CF6),
    this.inactiveColor = Colors.grey,
  });

  @override
  State<AnimatedVolumeSlider> createState() => _AnimatedVolumeSliderState();
}

class _AnimatedVolumeSliderState extends State<AnimatedVolumeSlider>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleChangeStart(double value) {
    setState(() => _isDragging = true);
    _controller.forward();
    VibifyMicroInteractions.lightImpact();
  }

  void _handleChangeEnd(double value) {
    setState(() => _isDragging = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: widget.activeColor,
              inactiveTrackColor: widget.inactiveColor.withValues(alpha: 0.3),
              thumbColor: widget.activeColor,
              overlayColor: widget.activeColor.withValues(alpha: 0.2),
              trackHeight: _isDragging ? 6 : 4,
              thumbShape: RoundSliderThumbShape(
                enabledThumbRadius: _isDragging ? 12 : 10,
              ),
            ),
            child: Slider(
              value: widget.value,
              onChanged: widget.onChanged,
              onChangeStart: _handleChangeStart,
              onChangeEnd: _handleChangeEnd,
            ),
          ),
        );
      },
    );
  }
}

/// Animated download progress with pulse effect
class AnimatedDownloadProgress extends StatefulWidget {
  final double progress;
  final bool isDownloading;
  final VoidCallback? onTap;
  final double size;

  const AnimatedDownloadProgress({
    super.key,
    required this.progress,
    required this.isDownloading,
    this.onTap,
    this.size = 32,
  });

  @override
  State<AnimatedDownloadProgress> createState() => _AnimatedDownloadProgressState();
}

class _AnimatedDownloadProgressState extends State<AnimatedDownloadProgress>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0)
        .animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));

    if (widget.isDownloading) {
      _rotationController.repeat();
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(AnimatedDownloadProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isDownloading && !oldWidget.isDownloading) {
      _rotationController.repeat();
      _pulseController.repeat(reverse: true);
    } else if (!widget.isDownloading && oldWidget.isDownloading) {
      _rotationController.stop();
      _pulseController.stop();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Background circle
            Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
                border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              ),
            ),
            
            // Progress indicator
            if (widget.isDownloading) ...[
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: SizedBox(
                      width: widget.size - 4,
                      height: widget.size - 4,
                      child: CircularProgressIndicator(
                        value: widget.progress,
                        strokeWidth: 2,
                        valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF8B5CF6)),
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                  );
                },
              ),
              
              // Rotating icon
              AnimatedBuilder(
                animation: _rotationController,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotationController.value * 2 * math.pi,
                    child: Icon(
                      Icons.download,
                      size: widget.size * 0.5,
                      color: Colors.white,
                    ),
                  );
                },
              ),
            ] else ...[
              // Static download icon
              Icon(
                widget.progress >= 1.0 ? Icons.download_done : Icons.download,
                size: widget.size * 0.5,
                color: widget.progress >= 1.0 ? Colors.green : Colors.white,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Animated search bar with focus effects
class AnimatedSearchBar extends StatefulWidget {
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final String hintText;
  final bool autofocus;

  const AnimatedSearchBar({
    super.key,
    this.controller,
    this.onChanged,
    this.onTap,
    this.hintText = 'Search music...',
    this.autofocus = false,
  });

  @override
  State<AnimatedSearchBar> createState() => _AnimatedSearchBarState();
}

class _AnimatedSearchBarState extends State<AnimatedSearchBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _colorAnimation = ColorTween(
      begin: Colors.white.withValues(alpha: 0.1),
      end: Colors.white.withValues(alpha: 0.15),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: _colorAnimation.value,
              border: Border.all(
                color: _focusNode.hasFocus 
                    ? const Color(0xFF8B5CF6).withValues(alpha: 0.5)
                    : Colors.white.withValues(alpha: 0.2),
                width: _focusNode.hasFocus ? 2 : 1,
              ),
            ),
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              autofocus: widget.autofocus,
              onChanged: widget.onChanged,
              onTap: widget.onTap,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.6)),
                prefixIcon: Icon(
                  Icons.search,
                  color: _focusNode.hasFocus 
                      ? const Color(0xFF8B5CF6)
                      : Colors.white.withValues(alpha: 0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
