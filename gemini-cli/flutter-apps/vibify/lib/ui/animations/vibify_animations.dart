import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Comprehensive animation system for Vibify
class VibifyAnimations {
  // Animation durations
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration extraSlow = Duration(milliseconds: 800);

  // Animation curves
  static const Curve bounceIn = Curves.elasticOut;
  static const Curve smoothIn = Curves.easeInOut;
  static const Curve quickIn = Curves.easeOut;
  static const Curve slideIn = Curves.easeInOutCubic;
}

/// Animated glass container with entrance animations
class AnimatedGlassContainer extends StatefulWidget {
  final Widget child;
  final Duration delay;
  final Duration duration;
  final Curve curve;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final bool slideFromBottom;
  final bool fadeIn;
  final bool scaleIn;

  const AnimatedGlassContainer({
    super.key,
    required this.child,
    this.delay = Duration.zero,
    this.duration = VibifyAnimations.medium,
    this.curve = VibifyAnimations.smoothIn,
    this.margin,
    this.padding,
    this.borderRadius,
    this.slideFromBottom = true,
    this.fadeIn = true,
    this.scaleIn = false,
  });

  @override
  State<AnimatedGlassContainer> createState() => _AnimatedGlassContainerState();
}

class _AnimatedGlassContainerState extends State<AnimatedGlassContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _slideAnimation = Tween<Offset>(
      begin: widget.slideFromBottom ? const Offset(0, 0.3) : const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _scaleAnimation = Tween<double>(
      begin: widget.scaleIn ? 0.8 : 1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    // Start animation after delay
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        Widget animatedChild = widget.child;

        if (widget.scaleIn) {
          animatedChild = Transform.scale(
            scale: _scaleAnimation.value,
            child: animatedChild,
          );
        }

        if (widget.slideFromBottom || !widget.slideFromBottom) {
          animatedChild = SlideTransition(
            position: _slideAnimation,
            child: animatedChild,
          );
        }

        if (widget.fadeIn) {
          animatedChild = FadeTransition(
            opacity: _fadeAnimation,
            child: animatedChild,
          );
        }

        return Container(
          margin: widget.margin,
          padding: widget.padding,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
            color: Colors.white.withValues(alpha: 0.1),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: animatedChild,
        );
      },
    );
  }
}

/// Animated button with press effects and haptic feedback
class AnimatedGlassButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Duration duration;
  final double pressScale;
  final bool hapticFeedback;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const AnimatedGlassButton({
    super.key,
    required this.child,
    this.onTap,
    this.duration = VibifyAnimations.fast,
    this.pressScale = 0.95,
    this.hapticFeedback = true,
    this.padding,
    this.borderRadius,
  });

  @override
  State<AnimatedGlassButton> createState() => _AnimatedGlassButtonState();
}

class _AnimatedGlassButtonState extends State<AnimatedGlassButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.pressScale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: AnimatedContainer(
              duration: widget.duration,
              padding: widget.padding ?? const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
                color: _isPressed 
                    ? Colors.white.withValues(alpha: 0.15)
                    : Colors.white.withValues(alpha: 0.1),
                border: Border.all(
                  color: _isPressed 
                      ? Colors.white.withValues(alpha: 0.3)
                      : Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: _isPressed ? [] : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: widget.child,
            ),
          );
        },
      ),
    );
  }
}

/// Floating action button with pulse animation
class PulsingFloatingButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final double size;

  const PulsingFloatingButton({
    super.key,
    required this.child,
    this.onPressed,
    this.backgroundColor,
    this.size = 56,
  });

  @override
  State<PulsingFloatingButton> createState() => _PulsingFloatingButtonState();
}

class _PulsingFloatingButtonState extends State<PulsingFloatingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  widget.backgroundColor ?? const Color(0xFF8B5CF6),
                  widget.backgroundColor?.withValues(alpha: 0.8) ?? const Color(0xFF7C3AED),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: (widget.backgroundColor ?? const Color(0xFF8B5CF6)).withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(widget.size / 2),
                onTap: widget.onPressed,
                child: Center(child: widget.child),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Staggered list animation for smooth list item appearances
class StaggeredListAnimation extends StatelessWidget {
  final List<Widget> children;
  final Duration delay;
  final Duration itemDelay;
  final Axis direction;

  const StaggeredListAnimation({
    super.key,
    required this.children,
    this.delay = Duration.zero,
    this.itemDelay = const Duration(milliseconds: 100),
    this.direction = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return AnimatedGlassContainer(
          delay: delay + (itemDelay * index),
          slideFromBottom: direction == Axis.vertical,
          child: child,
        );
      }).toList(),
    );
  }
}

/// Rotating music note animation for loading states
class RotatingMusicNote extends StatefulWidget {
  final double size;
  final Color color;
  final Duration duration;

  const RotatingMusicNote({
    super.key,
    this.size = 24,
    this.color = Colors.white,
    this.duration = const Duration(seconds: 2),
  });

  @override
  State<RotatingMusicNote> createState() => _RotatingMusicNoteState();
}

class _RotatingMusicNoteState extends State<RotatingMusicNote>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _controller.value * 2 * math.pi,
          child: Icon(
            Icons.music_note,
            size: widget.size,
            color: widget.color,
          ),
        );
      },
    );
  }
}
