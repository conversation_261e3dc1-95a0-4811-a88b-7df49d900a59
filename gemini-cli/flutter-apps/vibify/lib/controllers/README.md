# Controllers Directory

This directory contains state management controllers using Provider pattern.

## Planned Controllers:

- `audio_controller.dart` - Audio playback state management
- `playlist_controller.dart` - Playlist management and operations
- `download_controller.dart` - Download queue and progress management
- `theme_controller.dart` - Theme and UI state management
- `search_controller.dart` - YouTube search state management
- `library_controller.dart` - Music library state management

All controllers will:
- Extend ChangeNotifier for Provider integration
- Handle loading states and error states
- Provide clean APIs for UI components
- Implement proper disposal of resources
