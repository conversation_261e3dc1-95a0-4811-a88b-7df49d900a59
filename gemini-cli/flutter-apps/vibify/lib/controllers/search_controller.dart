import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/song.dart';
import '../services/search_service.dart';

enum SearchCategory { all, songs, artists, albums, playlists }

/// Controller for managing search state and operations
class SearchController extends ChangeNotifier {
  final SearchService _searchService = SearchService.instance;
  
  // Current state
  String _currentQuery = '';
  List<Song> _searchResults = [];
  List<String> _suggestions = [];
  List<Song> _trendingMusic = [];
  bool _isLoading = false;
  String? _errorMessage;
  SearchCategory _selectedCategory = SearchCategory.all;

  // Stream subscriptions
  StreamSubscription<List<Song>>? _resultsSubscription;
  StreamSubscription<List<String>>? _suggestionsSubscription;
  StreamSubscription<bool>? _loadingSubscription;

  // Search filters
  String _selectedGenre = '';
  String _selectedMood = '';
  bool _showOnlyDownloaded = false;

  // Getters
  String get currentQuery => _currentQuery;
  List<Song> get searchResults => List.unmodifiable(_searchResults);
  List<String> get suggestions => List.unmodifiable(_suggestions);
  List<Song> get trendingMusic => List.unmodifiable(_trendingMusic);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  SearchCategory get selectedCategory => _selectedCategory;
  String get selectedGenre => _selectedGenre;
  String get selectedMood => _selectedMood;
  bool get showOnlyDownloaded => _showOnlyDownloaded;

  // Computed getters
  bool get hasResults => _searchResults.isNotEmpty;
  bool get hasQuery => _currentQuery.isNotEmpty;
  bool get hasTrending => _trendingMusic.isNotEmpty;
  List<String> get searchHistory => _searchService.searchHistory;
  List<String> get popularSearches => _searchService.popularSearches;
  List<String> get popularGenres => _searchService.getPopularGenres();
  List<String> get moodSearches => _searchService.getMoodSearches();

  /// Initialize the search controller
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      // Initialize search service
      await _searchService.initialize();

      // Subscribe to search streams
      _resultsSubscription = _searchService.searchResultsStream.listen(_onSearchResults);
      _loadingSubscription = _searchService.loadingStream.listen(_onLoadingChanged);

      // Load trending music
      await loadTrendingMusic();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _handleError('Failed to initialize search: $e');
    }
  }

  /// Search for music
  Future<void> searchMusic(String query, {bool immediate = false}) async {
    try {
      _clearError();
      _currentQuery = query.trim();
      
      if (_currentQuery.isEmpty) {
        _searchResults.clear();
        notifyListeners();
        return;
      }

      await _searchService.searchMusic(_currentQuery, immediate: immediate);
    } catch (e) {
      _handleError('Search failed: $e');
    }
  }

  /// Get search suggestions
  void getSuggestions(String query) {
    try {
      _suggestions = _searchService.getSuggestions(query);
      notifyListeners();
    } catch (e) {
      _handleError('Failed to get suggestions: $e');
    }
  }

  /// Load trending music
  Future<void> loadTrendingMusic() async {
    try {
      _clearError();
      _trendingMusic = await _searchService.getTrendingMusic();
      notifyListeners();
    } catch (e) {
      _handleError('Failed to load trending music: $e');
    }
  }

  /// Search by genre
  Future<void> searchByGenre(String genre) async {
    try {
      _clearError();
      _selectedGenre = genre;
      _searchResults = await _searchService.getMusicByGenre(genre);
      _currentQuery = '$genre music';
      notifyListeners();
    } catch (e) {
      _handleError('Failed to search by genre: $e');
    }
  }

  /// Search by mood
  Future<void> searchByMood(String mood) async {
    try {
      _clearError();
      _selectedMood = mood;
      await searchMusic(mood, immediate: true);
    } catch (e) {
      _handleError('Failed to search by mood: $e');
    }
  }

  /// Search by artist
  Future<void> searchByArtist(String artist) async {
    try {
      _clearError();
      _searchResults = await _searchService.searchByArtist(artist);
      _currentQuery = artist;
      notifyListeners();
    } catch (e) {
      _handleError('Failed to search by artist: $e');
    }
  }

  /// Get related songs
  Future<List<Song>> getRelatedSongs(Song song) async {
    try {
      _clearError();
      return await _searchService.getRelatedSongs(song);
    } catch (e) {
      _handleError('Failed to get related songs: $e');
      return [];
    }
  }

  /// Search similar songs
  Future<List<Song>> searchSimilar(String title, String artist) async {
    try {
      _clearError();
      return await _searchService.searchSimilar(title, artist);
    } catch (e) {
      _handleError('Failed to search similar songs: $e');
      return [];
    }
  }

  /// Set search category
  void setSearchCategory(SearchCategory category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilters();
      notifyListeners();
    }
  }

  /// Toggle downloaded filter
  void toggleDownloadedFilter() {
    _showOnlyDownloaded = !_showOnlyDownloaded;
    _applyFilters();
    notifyListeners();
  }

  /// Clear current search
  void clearSearch() {
    _currentQuery = '';
    _searchResults.clear();
    _suggestions.clear();
    _selectedGenre = '';
    _selectedMood = '';
    _clearError();
    notifyListeners();
  }

  /// Clear search history
  Future<void> clearSearchHistory() async {
    try {
      await _searchService.clearSearchHistory();
      notifyListeners();
    } catch (e) {
      _handleError('Failed to clear search history: $e');
    }
  }

  /// Remove item from search history
  Future<void> removeFromSearchHistory(String query) async {
    try {
      await _searchService.removeFromSearchHistory(query);
      notifyListeners();
    } catch (e) {
      _handleError('Failed to remove from search history: $e');
    }
  }

  /// Apply filters to search results
  void _applyFilters() {
    // For now, we'll implement basic filtering
    // In a real app, you might want more sophisticated filtering
    if (_showOnlyDownloaded) {
      // Filter would be applied here if we had download status
    }
  }

  /// Handle search results updates
  void _onSearchResults(List<Song> results) {
    _searchResults = results;
    _applyFilters();
    notifyListeners();
  }

  /// Handle loading state changes
  void _onLoadingChanged(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Handle errors
  void _handleError(String message) {
    _errorMessage = message;
    _isLoading = false;
    notifyListeners();
    debugPrint('SearchController Error: $message');
  }

  /// Clear error state
  void _clearError() {
    _errorMessage = null;
  }

  /// Clear error manually
  void clearError() {
    _clearError();
    notifyListeners();
  }

  /// Refresh search results
  Future<void> refresh() async {
    if (_currentQuery.isNotEmpty) {
      await searchMusic(_currentQuery, immediate: true);
    } else {
      await loadTrendingMusic();
    }
  }

  /// Get filtered results by category
  List<Song> getFilteredResults() {
    switch (_selectedCategory) {
      case SearchCategory.all:
        return _searchResults;
      case SearchCategory.songs:
        return _searchResults; // All results are songs in our case
      case SearchCategory.artists:
        // Group by artist and return representative songs
        final artistMap = <String, Song>{};
        for (final song in _searchResults) {
          if (!artistMap.containsKey(song.artist)) {
            artistMap[song.artist] = song;
          }
        }
        return artistMap.values.toList();
      case SearchCategory.albums:
        // Group by album and return representative songs
        final albumMap = <String, Song>{};
        for (final song in _searchResults) {
          final albumKey = '${song.artist} - ${song.album ?? 'Unknown Album'}';
          if (!albumMap.containsKey(albumKey)) {
            albumMap[albumKey] = song;
          }
        }
        return albumMap.values.toList();
      case SearchCategory.playlists:
        // For now, return empty as we don't have playlist search
        return [];
    }
  }

  /// Get search statistics
  Map<String, int> getSearchStats() {
    return {
      'total_results': _searchResults.length,
      'cache_size': _searchService.getCacheSize(),
      'history_count': searchHistory.length,
    };
  }

  @override
  void dispose() {
    _resultsSubscription?.cancel();
    _suggestionsSubscription?.cancel();
    _loadingSubscription?.cancel();
    super.dispose();
  }
}
