import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/song.dart';
import '../models/playlist.dart';
import '../models/artist.dart';
import '../models/album.dart';

enum SortOption { title, artist, album, dateAdded, duration, playCount }

enum FilterOption { all, downloaded, favorites, recent }

class LibraryController extends ChangeNotifier {
  // Hive boxes
  Box<Song>? _songsBox;
  Box<Playlist>? _playlistsBox;
  Box<Artist>? _artistsBox;
  Box<Album>? _albumsBox;

  // Current state
  List<Song> _allSongs = [];
  List<Song> _filteredSongs = [];
  List<Playlist> _playlists = [];
  List<Artist> _artists = [];
  List<Album> _albums = [];

  // Filter and sort settings
  SortOption _currentSort = SortOption.title;
  bool _sortAscending = true;
  FilterOption _currentFilter = FilterOption.all;
  String _searchQuery = '';

  // Loading state
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _errorMessage;

  // Getters
  List<Song> get allSongs => List.unmodifiable(_allSongs);
  List<Song> get filteredSongs => List.unmodifiable(_filteredSongs);
  List<Playlist> get playlists => List.unmodifiable(_playlists);
  List<Artist> get artists => List.unmodifiable(_artists);
  List<Album> get albums => List.unmodifiable(_albums);
  SortOption get currentSort => _currentSort;
  bool get sortAscending => _sortAscending;
  FilterOption get currentFilter => _currentFilter;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get errorMessage => _errorMessage;

  // Helper method to check if boxes are initialized
  bool get _boxesInitialized =>
      _songsBox != null &&
      _playlistsBox != null &&
      _artistsBox != null &&
      _albumsBox != null;

  // Computed getters
  List<Song> get favoriteSongs =>
      _allSongs.where((song) => song.isFavorite).toList();
  List<Song> get downloadedSongs =>
      _allSongs.where((song) => song.isDownloaded).toList();
  List<Song> get recentSongs {
    final now = DateTime.now();
    final sevenDaysAgo = now.subtract(const Duration(days: 7));
    return _allSongs
        .where((song) => song.dateAdded.isAfter(sevenDaysAgo))
        .toList();
  }

  // System playlists
  Playlist get favoritesPlaylist => Playlist.system(
    id: 'favorites',
    name: 'Favorites',
    description: 'Your favorite songs',
  );

  Playlist get downloadsPlaylist => Playlist.system(
    id: 'downloads',
    name: 'Downloads',
    description: 'Downloaded songs',
  );

  Playlist get recentPlaylist => Playlist.system(
    id: 'recent',
    name: 'Recently Added',
    description: 'Songs added in the last 7 days',
  );

  // Initialize the library
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      // Open Hive boxes
      _songsBox = await Hive.openBox<Song>('songs');
      _playlistsBox = await Hive.openBox<Playlist>('playlists');
      _artistsBox = await Hive.openBox<Artist>('artists');
      _albumsBox = await Hive.openBox<Album>('albums');

      // Load data from storage
      await _loadAllData();

      _isInitialized = true;
      _isLoading = false;
      notifyListeners();
      debugPrint('LibraryController initialized successfully');
    } catch (e) {
      _handleError('Failed to initialize library: $e');
    }
  }

  // Load all data from Hive
  Future<void> _loadAllData() async {
    _allSongs = _songsBox?.values.toList() ?? [];
    _playlists = _playlistsBox?.values.toList() ?? [];
    _artists = _artistsBox?.values.toList() ?? [];
    _albums = _albumsBox?.values.toList() ?? [];

    _applyFiltersAndSort();
  }

  // Song management
  Future<void> addSong(Song song) async {
    try {
      if (_songsBox == null) {
        debugPrint('LibraryController Error: Songs box not initialized');
        return;
      }

      await _songsBox!.put(song.id, song);
      _allSongs.add(song);

      // Update artist and album
      await _updateArtistForSong(song);
      await _updateAlbumForSong(song);

      _applyFiltersAndSort();
      notifyListeners();
    } catch (e) {
      _handleError('Failed to add song: $e');
    }
  }

  Future<void> updateSong(Song song) async {
    try {
      if (!_boxesInitialized) {
        debugPrint('LibraryController Error: Boxes not initialized');
        return;
      }

      await _songsBox!.put(song.id, song);

      final index = _allSongs.indexWhere((s) => s.id == song.id);
      if (index != -1) {
        _allSongs[index] = song;
      }

      _applyFiltersAndSort();
      notifyListeners();
    } catch (e) {
      _handleError('Failed to update song: $e');
    }
  }

  Future<void> removeSong(String songId) async {
    try {
      if (!_boxesInitialized) {
        debugPrint('LibraryController Error: Boxes not initialized');
        return;
      }

      await _songsBox!.delete(songId);
      _allSongs.removeWhere((song) => song.id == songId);

      // Remove from playlists
      for (final playlist in _playlists) {
        playlist.removeSong(songId);
      }

      _applyFiltersAndSort();
      notifyListeners();
    } catch (e) {
      _handleError('Failed to remove song: $e');
    }
  }

  Future<void> toggleFavorite(String songId) async {
    try {
      final song = _allSongs.firstWhere((s) => s.id == songId);
      final updatedSong = song.copyWith(isFavorite: !song.isFavorite);
      await updateSong(updatedSong);
    } catch (e) {
      _handleError('Failed to toggle favorite: $e');
    }
  }

  // Playlist management
  Future<void> createPlaylist(String name, {String? description}) async {
    try {
      if (!_boxesInitialized) {
        debugPrint('LibraryController Error: Boxes not initialized');
        return;
      }

      final playlist = Playlist(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        description: description,
      );

      await _playlistsBox!.put(playlist.id, playlist);
      _playlists.add(playlist);
      notifyListeners();
    } catch (e) {
      _handleError('Failed to create playlist: $e');
    }
  }

  Future<void> updatePlaylist(Playlist playlist) async {
    try {
      await _playlistsBox.put(playlist.id, playlist);

      final index = _playlists.indexWhere((p) => p.id == playlist.id);
      if (index != -1) {
        _playlists[index] = playlist;
      }

      notifyListeners();
    } catch (e) {
      _handleError('Failed to update playlist: $e');
    }
  }

  Future<void> deletePlaylist(String playlistId) async {
    try {
      await _playlistsBox.delete(playlistId);
      _playlists.removeWhere((playlist) => playlist.id == playlistId);
      notifyListeners();
    } catch (e) {
      _handleError('Failed to delete playlist: $e');
    }
  }

  Future<void> addSongToPlaylist(String playlistId, String songId) async {
    try {
      final playlist = _playlists.firstWhere((p) => p.id == playlistId);
      playlist.addSong(songId);
      await updatePlaylist(playlist);
    } catch (e) {
      _handleError('Failed to add song to playlist: $e');
    }
  }

  Future<void> removeSongFromPlaylist(String playlistId, String songId) async {
    try {
      final playlist = _playlists.firstWhere((p) => p.id == playlistId);
      playlist.removeSong(songId);
      await updatePlaylist(playlist);
    } catch (e) {
      _handleError('Failed to remove song from playlist: $e');
    }
  }

  // Get songs for a playlist
  List<Song> getPlaylistSongs(Playlist playlist) {
    if (playlist.id == 'favorites') {
      return favoriteSongs;
    } else if (playlist.id == 'downloads') {
      return downloadedSongs;
    } else if (playlist.id == 'recent') {
      return recentSongs;
    } else {
      return _allSongs
          .where((song) => playlist.songIds.contains(song.id))
          .toList();
    }
  }

  // Search and filter
  void setSearchQuery(String query) {
    _searchQuery = query.toLowerCase();
    _applyFiltersAndSort();
    notifyListeners();
  }

  void setFilter(FilterOption filter) {
    _currentFilter = filter;
    _applyFiltersAndSort();
    notifyListeners();
  }

  void setSortOption(SortOption sort, {bool? ascending}) {
    _currentSort = sort;
    if (ascending != null) {
      _sortAscending = ascending;
    }
    _applyFiltersAndSort();
    notifyListeners();
  }

  void toggleSortOrder() {
    _sortAscending = !_sortAscending;
    _applyFiltersAndSort();
    notifyListeners();
  }

  // Apply current filters and sorting
  void _applyFiltersAndSort() {
    List<Song> filtered = List.from(_allSongs);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((song) {
        return song.title.toLowerCase().contains(_searchQuery) ||
            song.artist.toLowerCase().contains(_searchQuery) ||
            (song.album?.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }

    // Apply category filter
    switch (_currentFilter) {
      case FilterOption.downloaded:
        filtered = filtered.where((song) => song.isDownloaded).toList();
        break;
      case FilterOption.favorites:
        filtered = filtered.where((song) => song.isFavorite).toList();
        break;
      case FilterOption.recent:
        final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
        filtered = filtered
            .where((song) => song.dateAdded.isAfter(sevenDaysAgo))
            .toList();
        break;
      case FilterOption.all:
        // No additional filtering
        break;
    }

    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;

      switch (_currentSort) {
        case SortOption.title:
          comparison = a.title.compareTo(b.title);
          break;
        case SortOption.artist:
          comparison = a.artist.compareTo(b.artist);
          break;
        case SortOption.album:
          comparison = (a.album ?? '').compareTo(b.album ?? '');
          break;
        case SortOption.dateAdded:
          comparison = a.dateAdded.compareTo(b.dateAdded);
          break;
        case SortOption.duration:
          comparison = (a.duration?.inMilliseconds ?? 0).compareTo(
            b.duration?.inMilliseconds ?? 0,
          );
          break;
        case SortOption.playCount:
          // TODO: Implement play count tracking
          comparison = 0;
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    _filteredSongs = filtered;
  }

  // Helper methods for artist and album management
  Future<void> _updateArtistForSong(Song song) async {
    try {
      Artist? artist = _artists.firstWhere(
        (a) => a.name == song.artist,
        orElse: () => Artist(
          id: song.artist.toLowerCase().replaceAll(' ', '_'),
          name: song.artist,
        ),
      );

      if (!_artists.contains(artist)) {
        _artists.add(artist);
      }

      artist.addSong(song.id);
      await _artistsBox.put(artist.id, artist);
    } catch (e) {
      debugPrint('Failed to update artist: $e');
    }
  }

  Future<void> _updateAlbumForSong(Song song) async {
    if (song.album == null) return;

    try {
      Album? album = _albums.firstWhere(
        (a) => a.title == song.album && a.artistName == song.artist,
        orElse: () => Album(
          id: '${song.artist}_${song.album}'.toLowerCase().replaceAll(' ', '_'),
          title: song.album!,
          artistId: song.artist.toLowerCase().replaceAll(' ', '_'),
          artistName: song.artist,
        ),
      );

      if (!_albums.contains(album)) {
        _albums.add(album);
      }

      album.addSong(song.id);
      await _albumsBox.put(album.id, album);
    } catch (e) {
      debugPrint('Failed to update album: $e');
    }
  }

  // Error handling
  void _handleError(String message) {
    _errorMessage = message;
    _isLoading = false;
    notifyListeners();
    debugPrint('LibraryController Error: $message');
  }

  void _clearError() {
    _errorMessage = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  @override
  void dispose() {
    _songsBox.close();
    _playlistsBox.close();
    _artistsBox.close();
    _albumsBox.close();
    super.dispose();
  }
}
