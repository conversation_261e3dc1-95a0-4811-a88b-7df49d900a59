import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import '../models/song.dart';
import '../models/playlist.dart';

enum PlaybackState {
  stopped,
  playing,
  paused,
  loading,
  buffering,
  error,
}

enum RepeatMode {
  off,
  one,
  all,
}

class AudioController extends ChangeNotifier {
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // Current playback state
  PlaybackState _playbackState = PlaybackState.stopped;
  Song? _currentSong;
  Playlist? _currentPlaylist;
  List<Song> _queue = [];
  int _currentIndex = 0;
  
  // Playback settings
  bool _isShuffleEnabled = false;
  RepeatMode _repeatMode = RepeatMode.off;
  double _volume = 1.0;
  
  // Progress tracking
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  
  // Error handling
  String? _errorMessage;

  // Getters
  PlaybackState get playbackState => _playbackState;
  Song? get currentSong => _currentSong;
  Playlist? get currentPlaylist => _currentPlaylist;
  List<Song> get queue => List.unmodifiable(_queue);
  int get currentIndex => _currentIndex;
  bool get isShuffleEnabled => _isShuffleEnabled;
  RepeatMode get repeatMode => _repeatMode;
  double get volume => _volume;
  Duration get position => _position;
  Duration get duration => _duration;
  String? get errorMessage => _errorMessage;
  
  // Computed properties
  bool get isPlaying => _playbackState == PlaybackState.playing;
  bool get isPaused => _playbackState == PlaybackState.paused;
  bool get isLoading => _playbackState == PlaybackState.loading;
  bool get hasError => _playbackState == PlaybackState.error;
  bool get hasNext => _currentIndex < _queue.length - 1;
  bool get hasPrevious => _currentIndex > 0;
  double get progress => _duration.inMilliseconds > 0 
      ? _position.inMilliseconds / _duration.inMilliseconds 
      : 0.0;

  AudioController() {
    _initializeAudioPlayer();
  }

  void _initializeAudioPlayer() {
    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((playerState) {
      switch (playerState.processingState) {
        case ProcessingState.idle:
          _playbackState = PlaybackState.stopped;
          break;
        case ProcessingState.loading:
        case ProcessingState.buffering:
          _playbackState = PlaybackState.loading;
          break;
        case ProcessingState.ready:
          _playbackState = playerState.playing 
              ? PlaybackState.playing 
              : PlaybackState.paused;
          break;
        case ProcessingState.completed:
          _onSongCompleted();
          break;
      }
      notifyListeners();
    });

    // Listen to position changes
    _audioPlayer.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });

    // Listen to duration changes
    _audioPlayer.durationStream.listen((duration) {
      _duration = duration ?? Duration.zero;
      notifyListeners();
    });
  }

  // Play a single song
  Future<void> playSong(Song song) async {
    try {
      _clearError();
      _playbackState = PlaybackState.loading;
      _currentSong = song;
      _queue = [song];
      _currentIndex = 0;
      notifyListeners();

      await _loadAndPlayCurrentSong();
    } catch (e) {
      _handleError('Failed to play song: $e');
    }
  }

  // Play a playlist
  Future<void> playPlaylist(Playlist playlist, List<Song> songs, {int startIndex = 0}) async {
    try {
      _clearError();
      _playbackState = PlaybackState.loading;
      _currentPlaylist = playlist;
      _queue = songs;
      _currentIndex = startIndex.clamp(0, songs.length - 1);
      _currentSong = _queue.isNotEmpty ? _queue[_currentIndex] : null;
      notifyListeners();

      if (_currentSong != null) {
        await _loadAndPlayCurrentSong();
      }
    } catch (e) {
      _handleError('Failed to play playlist: $e');
    }
  }

  // Add song to queue
  void addToQueue(Song song) {
    _queue.add(song);
    notifyListeners();
  }

  // Add songs to queue
  void addSongsToQueue(List<Song> songs) {
    _queue.addAll(songs);
    notifyListeners();
  }

  // Remove song from queue
  void removeFromQueue(int index) {
    if (index >= 0 && index < _queue.length) {
      _queue.removeAt(index);
      if (index < _currentIndex) {
        _currentIndex--;
      } else if (index == _currentIndex && _queue.isNotEmpty) {
        _currentIndex = _currentIndex.clamp(0, _queue.length - 1);
        _currentSong = _queue[_currentIndex];
      } else if (_queue.isEmpty) {
        _currentSong = null;
        _currentIndex = 0;
      }
      notifyListeners();
    }
  }

  // Clear queue
  void clearQueue() {
    _queue.clear();
    _currentIndex = 0;
    _currentSong = null;
    _currentPlaylist = null;
    stop();
  }

  // Playback controls
  Future<void> play() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      _handleError('Failed to play: $e');
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      _handleError('Failed to pause: $e');
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _position = Duration.zero;
      notifyListeners();
    } catch (e) {
      _handleError('Failed to stop: $e');
    }
  }

  Future<void> togglePlayPause() async {
    if (isPlaying) {
      await pause();
    } else {
      await play();
    }
  }

  // Navigation controls
  Future<void> skipToNext() async {
    if (hasNext || _repeatMode == RepeatMode.all) {
      if (_repeatMode == RepeatMode.all && !hasNext) {
        _currentIndex = 0;
      } else {
        _currentIndex++;
      }
      _currentSong = _queue[_currentIndex];
      await _loadAndPlayCurrentSong();
    }
  }

  Future<void> skipToPrevious() async {
    if (hasPrevious || _repeatMode == RepeatMode.all) {
      if (_repeatMode == RepeatMode.all && !hasPrevious) {
        _currentIndex = _queue.length - 1;
      } else {
        _currentIndex--;
      }
      _currentSong = _queue[_currentIndex];
      await _loadAndPlayCurrentSong();
    }
  }

  Future<void> skipToIndex(int index) async {
    if (index >= 0 && index < _queue.length) {
      _currentIndex = index;
      _currentSong = _queue[_currentIndex];
      await _loadAndPlayCurrentSong();
    }
  }

  // Seek to position
  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      _handleError('Failed to seek: $e');
    }
  }

  // Volume control
  Future<void> setVolume(double volume) async {
    try {
      _volume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(_volume);
      notifyListeners();
    } catch (e) {
      _handleError('Failed to set volume: $e');
    }
  }

  // Shuffle control
  void toggleShuffle() {
    _isShuffleEnabled = !_isShuffleEnabled;
    // TODO: Implement queue shuffling logic
    notifyListeners();
  }

  // Repeat mode control
  void toggleRepeatMode() {
    switch (_repeatMode) {
      case RepeatMode.off:
        _repeatMode = RepeatMode.all;
        break;
      case RepeatMode.all:
        _repeatMode = RepeatMode.one;
        break;
      case RepeatMode.one:
        _repeatMode = RepeatMode.off;
        break;
    }
    notifyListeners();
  }

  // Private methods
  Future<void> _loadAndPlayCurrentSong() async {
    if (_currentSong == null) return;

    try {
      String? audioSource;
      
      // Determine audio source (local file or YouTube URL)
      if (_currentSong!.localPath != null) {
        audioSource = _currentSong!.localPath!;
      } else if (_currentSong!.youtubeUrl != null) {
        // TODO: Extract audio URL from YouTube
        audioSource = _currentSong!.youtubeUrl!;
      }

      if (audioSource != null) {
        await _audioPlayer.setUrl(audioSource);
        await _audioPlayer.play();
      } else {
        _handleError('No audio source available for this song');
      }
    } catch (e) {
      _handleError('Failed to load song: $e');
    }
  }

  void _onSongCompleted() {
    if (_repeatMode == RepeatMode.one) {
      // Repeat current song
      _audioPlayer.seek(Duration.zero);
      _audioPlayer.play();
    } else if (hasNext || _repeatMode == RepeatMode.all) {
      // Play next song
      skipToNext();
    } else {
      // Stop playback
      _playbackState = PlaybackState.stopped;
      _position = Duration.zero;
      notifyListeners();
    }
  }

  void _handleError(String message) {
    _errorMessage = message;
    _playbackState = PlaybackState.error;
    notifyListeners();
    debugPrint('AudioController Error: $message');
  }

  void _clearError() {
    _errorMessage = null;
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
