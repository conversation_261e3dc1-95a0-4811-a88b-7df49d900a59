import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/song.dart';
import '../models/download.dart';
import '../services/download_service.dart';

/// Controller for managing download state and operations
class DownloadController extends ChangeNotifier {
  final DownloadService _downloadService = DownloadService.instance;
  
  // Current state
  List<Download> _downloads = [];
  Map<String, int> _downloadStats = {};
  bool _isLoading = false;
  String? _errorMessage;

  // Stream subscriptions
  StreamSubscription<List<Download>>? _queueSubscription;
  StreamSubscription<Download>? _updateSubscription;

  // Getters
  List<Download> get downloads => List.unmodifiable(_downloads);
  Map<String, int> get downloadStats => Map.unmodifiable(_downloadStats);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Computed getters
  List<Download> get activeDownloads => _downloads.where((d) => d.isActive).toList();
  List<Download> get completedDownloads => _downloads.where((d) => d.isCompleted).toList();
  List<Download> get failedDownloads => _downloads.where((d) => d.isFailed).toList();
  List<Download> get pendingDownloads => _downloads.where((d) => d.status == DownloadStatus.pending).toList();
  
  bool get hasActiveDownloads => activeDownloads.isNotEmpty;
  bool get hasCompletedDownloads => completedDownloads.isNotEmpty;
  bool get hasFailedDownloads => failedDownloads.isNotEmpty;
  
  int get totalDownloads => _downloads.length;
  int get completedCount => completedDownloads.length;
  int get activeCount => activeDownloads.length;
  int get failedCount => failedDownloads.length;

  /// Initialize the download controller
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      // Initialize download service
      await _downloadService.initialize();

      // Subscribe to download streams
      _queueSubscription = _downloadService.queueStream.listen(_onQueueUpdate);
      _updateSubscription = _downloadService.downloadUpdateStream.listen(_onDownloadUpdate);

      // Load initial data
      _downloads = _downloadService.downloadQueue;
      _updateStats();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _handleError('Failed to initialize downloads: $e');
    }
  }

  /// Download a song
  Future<String?> downloadSong(Song song) async {
    try {
      _clearError();
      
      // Check if song can be downloaded
      if (song.youtubeUrl == null || song.youtubeUrl!.isEmpty) {
        throw Exception('No YouTube URL available for download');
      }

      // Check if already downloaded or downloading
      final existingDownload = _downloads.firstWhere(
        (d) => d.songId == song.id,
        orElse: () => Download(
          id: '',
          songId: '',
          title: '',
          artist: '',
          youtubeUrl: '',
        ),
      );

      if (existingDownload.id.isNotEmpty) {
        if (existingDownload.isCompleted) {
          throw Exception('Song is already downloaded');
        } else if (existingDownload.isActive) {
          throw Exception('Song is already being downloaded');
        }
      }

      // Add to download queue
      final downloadId = await _downloadService.addToDownloadQueue(song);
      return downloadId;
    } catch (e) {
      _handleError('Failed to start download: $e');
      return null;
    }
  }

  /// Cancel a download
  Future<void> cancelDownload(String downloadId) async {
    try {
      _clearError();
      await _downloadService.cancelDownload(downloadId);
    } catch (e) {
      _handleError('Failed to cancel download: $e');
    }
  }

  /// Pause a download
  Future<void> pauseDownload(String downloadId) async {
    try {
      _clearError();
      await _downloadService.pauseDownload(downloadId);
    } catch (e) {
      _handleError('Failed to pause download: $e');
    }
  }

  /// Resume a paused download
  Future<void> resumeDownload(String downloadId) async {
    try {
      _clearError();
      await _downloadService.resumeDownload(downloadId);
    } catch (e) {
      _handleError('Failed to resume download: $e');
    }
  }

  /// Retry a failed download
  Future<void> retryDownload(String downloadId) async {
    try {
      _clearError();
      await _downloadService.retryDownload(downloadId);
    } catch (e) {
      _handleError('Failed to retry download: $e');
    }
  }

  /// Remove a download
  Future<void> removeDownload(String downloadId) async {
    try {
      _clearError();
      await _downloadService.removeDownload(downloadId);
    } catch (e) {
      _handleError('Failed to remove download: $e');
    }
  }

  /// Clear all completed downloads
  Future<void> clearCompletedDownloads() async {
    try {
      _clearError();
      await _downloadService.clearCompletedDownloads();
    } catch (e) {
      _handleError('Failed to clear completed downloads: $e');
    }
  }

  /// Get download progress stream for a specific download
  Stream<Download>? getDownloadProgressStream(String downloadId) {
    return _downloadService.getDownloadProgressStream(downloadId);
  }

  /// Check if a song is downloaded
  bool isSongDownloaded(String songId) {
    return _downloads.any((d) => d.songId == songId && d.isCompleted);
  }

  /// Check if a song is being downloaded
  bool isSongDownloading(String songId) {
    return _downloads.any((d) => d.songId == songId && d.isActive);
  }

  /// Get download for a song
  Download? getDownloadForSong(String songId) {
    try {
      return _downloads.firstWhere((d) => d.songId == songId);
    } catch (e) {
      return null;
    }
  }

  /// Get download by ID
  Download? getDownloadById(String downloadId) {
    try {
      return _downloads.firstWhere((d) => d.id == downloadId);
    } catch (e) {
      return null;
    }
  }

  /// Get downloads by status
  List<Download> getDownloadsByStatus(DownloadStatus status) {
    return _downloads.where((d) => d.status == status).toList();
  }

  /// Get total download progress (for all active downloads)
  double getTotalProgress() {
    if (activeDownloads.isEmpty) return 0.0;
    
    final totalProgress = activeDownloads.fold<double>(
      0.0,
      (sum, download) => sum + download.progress,
    );
    
    return totalProgress / activeDownloads.length;
  }

  /// Get total downloaded size
  int getTotalDownloadedSize() {
    return completedDownloads.fold<int>(
      0,
      (sum, download) => sum + (download.totalBytes ?? 0),
    );
  }

  /// Get formatted total downloaded size
  String getFormattedTotalSize() {
    final totalBytes = getTotalDownloadedSize();
    return _formatBytes(totalBytes);
  }

  /// Pause all active downloads
  Future<void> pauseAllDownloads() async {
    try {
      _clearError();
      final activeIds = activeDownloads.map((d) => d.id).toList();
      
      for (final id in activeIds) {
        await _downloadService.pauseDownload(id);
      }
    } catch (e) {
      _handleError('Failed to pause all downloads: $e');
    }
  }

  /// Resume all paused downloads
  Future<void> resumeAllDownloads() async {
    try {
      _clearError();
      final pausedDownloads = _downloads.where((d) => d.isPaused).toList();
      
      for (final download in pausedDownloads) {
        await _downloadService.resumeDownload(download.id);
      }
    } catch (e) {
      _handleError('Failed to resume all downloads: $e');
    }
  }

  /// Retry all failed downloads
  Future<void> retryAllFailedDownloads() async {
    try {
      _clearError();
      final failedIds = failedDownloads.map((d) => d.id).toList();
      
      for (final id in failedIds) {
        await _downloadService.retryDownload(id);
      }
    } catch (e) {
      _handleError('Failed to retry all failed downloads: $e');
    }
  }

  /// Handle queue updates
  void _onQueueUpdate(List<Download> downloads) {
    _downloads = downloads;
    _updateStats();
    notifyListeners();
  }

  /// Handle individual download updates
  void _onDownloadUpdate(Download download) {
    final index = _downloads.indexWhere((d) => d.id == download.id);
    if (index != -1) {
      _downloads[index] = download;
      _updateStats();
      notifyListeners();
    }
  }

  /// Update download statistics
  void _updateStats() {
    _downloadStats = _downloadService.getDownloadStats();
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes >= 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    } else if (bytes >= 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else if (bytes >= 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '$bytes B';
    }
  }

  /// Handle errors
  void _handleError(String message) {
    _errorMessage = message;
    _isLoading = false;
    notifyListeners();
    debugPrint('DownloadController Error: $message');
  }

  /// Clear error state
  void _clearError() {
    _errorMessage = null;
  }

  /// Clear error manually
  void clearError() {
    _clearError();
    notifyListeners();
  }

  @override
  void dispose() {
    _queueSubscription?.cancel();
    _updateSubscription?.cancel();
    super.dispose();
  }
}
