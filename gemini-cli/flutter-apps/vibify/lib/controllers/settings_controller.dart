import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/settings_service.dart';

/// Controller for managing settings state and operations
class SettingsController extends ChangeNotifier {
  final SettingsService _settingsService = SettingsService.instance;
  
  // Current state
  Map<String, dynamic> _settings = {};
  bool _isLoading = false;
  String? _errorMessage;

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _settingsSubscription;

  // Getters
  Map<String, dynamic> get settings => Map.unmodifiable(_settings);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Convenience getters for common settings
  AudioQuality get audioQuality => getSetting('audio_quality', defaultValue: AudioQuality.high);
  DownloadQuality get downloadQuality => getSetting('download_quality', defaultValue: DownloadQuality.high);
  AppThemeMode get themeMode => getSetting('theme_mode', defaultValue: AppThemeMode.system);
  double get volume => getSetting('volume', defaultValue: 0.8);
  double get glassIntensity => getSetting('glass_intensity', defaultValue: 0.8);
  bool get downloadOverWifiOnly => getSetting('download_over_wifi_only', defaultValue: true);
  bool get showNotifications => getSetting('show_notifications', defaultValue: true);
  bool get analyticsEnabled => getSetting('analytics_enabled', defaultValue: true);
  bool get developerMode => getSetting('developer_mode', defaultValue: false);

  /// Initialize the settings controller
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _clearError();
      notifyListeners();

      // Initialize settings service
      await _settingsService.initialize();

      // Subscribe to settings changes
      _settingsSubscription = _settingsService.settingsStream.listen(_onSettingsChanged);

      // Load initial settings
      _settings = _settingsService.getAllSettings();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _handleError('Failed to initialize settings: $e');
    }
  }

  /// Get a setting value
  T getSetting<T>(String key, {T? defaultValue}) {
    return _settingsService.getSetting<T>(key, defaultValue: defaultValue);
  }

  /// Set a setting value
  Future<void> setSetting<T>(String key, T value) async {
    try {
      _clearError();
      await _settingsService.setSetting<T>(key, value);
    } catch (e) {
      _handleError('Failed to update setting: $e');
    }
  }

  /// Set multiple settings
  Future<void> setSettings(Map<String, dynamic> settings) async {
    try {
      _clearError();
      await _settingsService.setSettings(settings);
    } catch (e) {
      _handleError('Failed to update settings: $e');
    }
  }

  /// Toggle a boolean setting
  Future<void> toggleSetting(String key) async {
    try {
      final currentValue = getSetting<bool>(key, defaultValue: false);
      await setSetting(key, !currentValue);
    } catch (e) {
      _handleError('Failed to toggle setting: $e');
    }
  }

  /// Update audio quality
  Future<void> setAudioQuality(AudioQuality quality) async {
    await setSetting('audio_quality', quality);
  }

  /// Update download quality
  Future<void> setDownloadQuality(DownloadQuality quality) async {
    await setSetting('download_quality', quality);
  }

  /// Update theme mode
  Future<void> setThemeMode(AppThemeMode mode) async {
    await setSetting('theme_mode', mode);
  }

  /// Update volume
  Future<void> setVolume(double volume) async {
    await setSetting('volume', volume.clamp(0.0, 1.0));
  }

  /// Update glass intensity
  Future<void> setGlassIntensity(double intensity) async {
    await setSetting('glass_intensity', intensity.clamp(0.0, 1.0));
  }

  /// Update crossfade duration
  Future<void> setCrossfadeDuration(double duration) async {
    await setSetting('crossfade_duration', duration.clamp(0.0, 10.0));
  }

  /// Update max concurrent downloads
  Future<void> setMaxConcurrentDownloads(int count) async {
    await setSetting('max_concurrent_downloads', count.clamp(1, 10));
  }

  /// Update cache size
  Future<void> setCacheSize(int sizeMB) async {
    await setSetting('cache_size_mb', sizeMB.clamp(100, 2000));
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    try {
      _clearError();
      await _settingsService.resetToDefaults();
    } catch (e) {
      _handleError('Failed to reset settings: $e');
    }
  }

  /// Reset specific category to defaults
  Future<void> resetCategoryToDefaults(String category) async {
    try {
      _clearError();
      await _settingsService.resetCategoryToDefaults(category);
    } catch (e) {
      _handleError('Failed to reset category: $e');
    }
  }

  /// Get formatted setting value for display
  String getFormattedValue(String key) {
    return _settingsService.getFormattedValue(key);
  }

  /// Get theme mode for Flutter
  ThemeMode getFlutterThemeMode() {
    switch (themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
      case AppThemeMode.glassDark:
        return ThemeMode.dark;
      case AppThemeMode.system:
      default:
        return ThemeMode.system;
    }
  }

  /// Check if dark mode is enabled
  bool isDarkMode(BuildContext context) {
    switch (themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
      case AppThemeMode.glassDark:
        return true;
      case AppThemeMode.system:
      default:
        return Theme.of(context).brightness == Brightness.dark;
    }
  }

  /// Get settings by category
  Map<String, dynamic> getCategorySettings(String category) {
    final categoryKeys = _getCategoryKeys(category);
    final result = <String, dynamic>{};
    for (final key in categoryKeys) {
      result[key] = _settings[key];
    }
    return result;
  }

  /// Get category keys
  List<String> _getCategoryKeys(String category) {
    switch (category) {
      case 'audio':
        return ['audio_quality', 'volume', 'crossfade_duration', 'normalize_volume', 'gapless_playback', 'replay_gain'];
      case 'download':
        return ['download_quality', 'download_over_wifi_only', 'auto_download_favorites', 'max_concurrent_downloads'];
      case 'appearance':
        return ['theme_mode', 'glass_intensity', 'show_album_art', 'show_lyrics', 'animated_backgrounds', 'reduce_motion'];
      case 'playback':
        return ['auto_play_next', 'shuffle_mode', 'repeat_mode', 'skip_silence', 'remember_playback_position'];
      case 'library':
        return ['auto_add_to_library', 'show_explicit_content', 'group_by_album_artist', 'sort_albums_by_year'];
      case 'search':
        return ['search_suggestions', 'save_search_history', 'trending_music_region', 'explicit_search_results'];
      case 'privacy':
        return ['analytics_enabled', 'crash_reporting', 'personalized_recommendations', 'share_listening_activity'];
      case 'notifications':
        return ['show_notifications', 'download_notifications', 'playback_notifications', 'new_music_notifications'];
      case 'advanced':
        return ['developer_mode', 'debug_logging', 'cache_size_mb', 'preload_next_track'];
      default:
        return [];
    }
  }

  /// Export settings for backup
  Map<String, dynamic> exportSettings() {
    return Map.from(_settings);
  }

  /// Import settings from backup
  Future<void> importSettings(Map<String, dynamic> importedSettings) async {
    try {
      _clearError();
      
      // Validate imported settings
      final validSettings = <String, dynamic>{};
      for (final entry in importedSettings.entries) {
        if (_settingsService.hasSetting(entry.key)) {
          validSettings[entry.key] = entry.value;
        }
      }
      
      await _settingsService.setSettings(validSettings);
    } catch (e) {
      _handleError('Failed to import settings: $e');
    }
  }

  /// Handle settings changes
  void _onSettingsChanged(Map<String, dynamic> settings) {
    _settings = settings;
    notifyListeners();
  }

  /// Handle errors
  void _handleError(String message) {
    _errorMessage = message;
    _isLoading = false;
    notifyListeners();
    debugPrint('SettingsController Error: $message');
  }

  /// Clear error state
  void _clearError() {
    _errorMessage = null;
  }

  /// Clear error manually
  void clearError() {
    _clearError();
    notifyListeners();
  }

  @override
  void dispose() {
    _settingsSubscription?.cancel();
    super.dispose();
  }
}
