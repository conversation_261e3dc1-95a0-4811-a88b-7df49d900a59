import 'package:hive/hive.dart';

/// Hive adapter for Duration type
class Duration<PERSON><PERSON>pter extends TypeAdapter<Duration> {
  @override
  final int typeId = 6; // Unique type ID for Duration

  @override
  Duration read(BinaryReader reader) {
    final microseconds = reader.readInt();
    return Duration(microseconds: microseconds);
  }

  @override
  void write(BinaryWriter writer, Duration obj) {
    writer.writeInt(obj.inMicroseconds);
  }
}
