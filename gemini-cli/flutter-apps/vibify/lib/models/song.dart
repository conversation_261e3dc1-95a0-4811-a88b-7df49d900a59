import 'package:hive/hive.dart';

part 'song.g.dart';

@HiveType(typeId: 0)
class Song extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String artist;

  @HiveField(3)
  String? album;

  @HiveField(4)
  Duration? duration;

  @HiveField(5)
  String? thumbnailUrl;

  @HiveField(6)
  String? localPath;

  @HiveField(7)
  String? youtubeUrl;

  @HiveField(8)
  bool isDownloaded;

  @HiveField(9)
  bool isFavorite;

  @HiveField(10)
  DateTime dateAdded;

  @HiveField(11)
  int? fileSize;

  @HiveField(12)
  String? genre;

  @HiveField(13)
  int? year;

  @HiveField(14)
  String? albumArtUrl;

  Song({
    required this.id,
    required this.title,
    required this.artist,
    this.album,
    this.duration,
    this.thumbnailUrl,
    this.localPath,
    this.youtubeUrl,
    this.isDownloaded = false,
    this.isFavorite = false,
    DateTime? dateAdded,
    this.fileSize,
    this.genre,
    this.year,
    this.albumArtUrl,
  }) : dateAdded = dateAdded ?? DateTime.now();

  // Factory constructor for creating Song from YouTube data
  factory Song.fromYoutube({
    required String id,
    required String title,
    required String artist,
    String? thumbnailUrl,
    Duration? duration,
    String? youtubeUrl,
  }) {
    return Song(
      id: id,
      title: title,
      artist: artist,
      thumbnailUrl: thumbnailUrl,
      duration: duration,
      youtubeUrl: youtubeUrl,
    );
  }

  // Factory constructor for creating Song from local file
  factory Song.fromLocal({
    required String id,
    required String title,
    required String artist,
    required String localPath,
    String? album,
    Duration? duration,
    int? fileSize,
    String? genre,
    int? year,
  }) {
    return Song(
      id: id,
      title: title,
      artist: artist,
      album: album,
      duration: duration,
      localPath: localPath,
      isDownloaded: true,
      fileSize: fileSize,
      genre: genre,
      year: year,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artist': artist,
      'album': album,
      'duration': duration?.inMilliseconds,
      'thumbnailUrl': thumbnailUrl,
      'localPath': localPath,
      'youtubeUrl': youtubeUrl,
      'isDownloaded': isDownloaded,
      'isFavorite': isFavorite,
      'dateAdded': dateAdded.toIso8601String(),
      'fileSize': fileSize,
      'genre': genre,
      'year': year,
      'albumArtUrl': albumArtUrl,
    };
  }

  // Create from JSON
  factory Song.fromJson(Map<String, dynamic> json) {
    return Song(
      id: json['id'],
      title: json['title'],
      artist: json['artist'],
      album: json['album'],
      duration: json['duration'] != null 
          ? Duration(milliseconds: json['duration']) 
          : null,
      thumbnailUrl: json['thumbnailUrl'],
      localPath: json['localPath'],
      youtubeUrl: json['youtubeUrl'],
      isDownloaded: json['isDownloaded'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      dateAdded: DateTime.parse(json['dateAdded']),
      fileSize: json['fileSize'],
      genre: json['genre'],
      year: json['year'],
      albumArtUrl: json['albumArtUrl'],
    );
  }

  // Get formatted duration
  String get formattedDuration {
    if (duration == null) return '--:--';
    final minutes = duration!.inMinutes;
    final seconds = duration!.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Get display name (title - artist)
  String get displayName => '$title - $artist';

  // Get file size in MB
  String get fileSizeFormatted {
    if (fileSize == null) return 'Unknown';
    final mb = fileSize! / (1024 * 1024);
    return '${mb.toStringAsFixed(1)} MB';
  }

  // Check if song has audio source
  bool get hasAudioSource => localPath != null || youtubeUrl != null;

  // Get primary image URL
  String? get primaryImageUrl => albumArtUrl ?? thumbnailUrl;

  @override
  String toString() {
    return 'Song(id: $id, title: $title, artist: $artist, isDownloaded: $isDownloaded)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Song && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method for updating song properties
  Song copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    Duration? duration,
    String? thumbnailUrl,
    String? localPath,
    String? youtubeUrl,
    bool? isDownloaded,
    bool? isFavorite,
    DateTime? dateAdded,
    int? fileSize,
    String? genre,
    int? year,
    String? albumArtUrl,
  }) {
    return Song(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      duration: duration ?? this.duration,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      localPath: localPath ?? this.localPath,
      youtubeUrl: youtubeUrl ?? this.youtubeUrl,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      isFavorite: isFavorite ?? this.isFavorite,
      dateAdded: dateAdded ?? this.dateAdded,
      fileSize: fileSize ?? this.fileSize,
      genre: genre ?? this.genre,
      year: year ?? this.year,
      albumArtUrl: albumArtUrl ?? this.albumArtUrl,
    );
  }
}
