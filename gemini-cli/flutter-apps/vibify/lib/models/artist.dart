import 'package:hive/hive.dart';

part 'artist.g.dart';

@HiveType(typeId: 2)
class Artist extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String? bio;

  @HiveField(3)
  String? imageUrl;

  @HiveField(4)
  List<String> songIds;

  @HiveField(5)
  List<String> albumIds;

  @HiveField(6)
  int playCount;

  @HiveField(7)
  DateTime dateAdded;

  @HiveField(8)
  String? genre;

  @HiveField(9)
  String? country;

  @HiveField(10)
  int? yearFormed;

  Artist({
    required this.id,
    required this.name,
    this.bio,
    this.imageUrl,
    List<String>? songIds,
    List<String>? albumIds,
    this.playCount = 0,
    DateTime? dateAdded,
    this.genre,
    this.country,
    this.yearFormed,
  }) : songIds = songIds ?? [],
       albumIds = albumIds ?? [],
       dateAdded = dateAdded ?? DateTime.now();

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'bio': bio,
      'imageUrl': imageUrl,
      'songIds': songIds,
      'albumIds': albumIds,
      'playCount': playCount,
      'dateAdded': dateAdded.toIso8601String(),
      'genre': genre,
      'country': country,
      'yearFormed': yearFormed,
    };
  }

  // Create from JSON
  factory Artist.fromJson(Map<String, dynamic> json) {
    return Artist(
      id: json['id'],
      name: json['name'],
      bio: json['bio'],
      imageUrl: json['imageUrl'],
      songIds: List<String>.from(json['songIds'] ?? []),
      albumIds: List<String>.from(json['albumIds'] ?? []),
      playCount: json['playCount'] ?? 0,
      dateAdded: DateTime.parse(json['dateAdded']),
      genre: json['genre'],
      country: json['country'],
      yearFormed: json['yearFormed'],
    );
  }

  // Get song count
  int get songCount => songIds.length;

  // Get album count
  int get albumCount => albumIds.length;

  // Add song to artist
  void addSong(String songId) {
    if (!songIds.contains(songId)) {
      songIds.add(songId);
      save(); // Save to Hive
    }
  }

  // Remove song from artist
  void removeSong(String songId) {
    if (songIds.remove(songId)) {
      save(); // Save to Hive
    }
  }

  // Add album to artist
  void addAlbum(String albumId) {
    if (!albumIds.contains(albumId)) {
      albumIds.add(albumId);
      save(); // Save to Hive
    }
  }

  // Remove album from artist
  void removeAlbum(String albumId) {
    if (albumIds.remove(albumId)) {
      save(); // Save to Hive
    }
  }

  // Increment play count
  void incrementPlayCount() {
    playCount++;
    save(); // Save to Hive
  }

  // Check if artist has songs
  bool get hasSongs => songIds.isNotEmpty;

  // Check if artist has albums
  bool get hasAlbums => albumIds.isNotEmpty;

  // Get formatted play count
  String get formattedPlayCount {
    if (playCount >= 1000000) {
      return '${(playCount / 1000000).toStringAsFixed(1)}M plays';
    } else if (playCount >= 1000) {
      return '${(playCount / 1000).toStringAsFixed(1)}K plays';
    } else {
      return '$playCount plays';
    }
  }

  // Get years active (if yearFormed is available)
  String? get yearsActive {
    if (yearFormed == null) return null;
    final currentYear = DateTime.now().year;
    final yearsCount = currentYear - yearFormed!;
    return '$yearsCount years active';
  }

  @override
  String toString() {
    return 'Artist(id: $id, name: $name, songCount: $songCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Artist && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method
  Artist copyWith({
    String? id,
    String? name,
    String? bio,
    String? imageUrl,
    List<String>? songIds,
    List<String>? albumIds,
    int? playCount,
    DateTime? dateAdded,
    String? genre,
    String? country,
    int? yearFormed,
  }) {
    return Artist(
      id: id ?? this.id,
      name: name ?? this.name,
      bio: bio ?? this.bio,
      imageUrl: imageUrl ?? this.imageUrl,
      songIds: songIds ?? List<String>.from(this.songIds),
      albumIds: albumIds ?? List<String>.from(this.albumIds),
      playCount: playCount ?? this.playCount,
      dateAdded: dateAdded ?? this.dateAdded,
      genre: genre ?? this.genre,
      country: country ?? this.country,
      yearFormed: yearFormed ?? this.yearFormed,
    );
  }
}
