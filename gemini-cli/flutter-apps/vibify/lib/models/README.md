# Models Directory

This directory contains all data models for the Vibify app.

## Planned Models:

- `song.dart` - Song model with metadata (title, artist, album, duration, etc.)
- `playlist.dart` - Playlist model for custom playlists
- `artist.dart` - Artist information model
- `album.dart` - Album information model
- `download.dart` - Download status and metadata model

All models will include:
- JSON serialization/deserialization
- Hive type adapters for local storage
- Proper null safety
- toString() and equality operators
