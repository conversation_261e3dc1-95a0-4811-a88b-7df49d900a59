import 'package:hive/hive.dart';
import 'song.dart';

part 'playlist.g.dart';

@HiveType(typeId: 1)
class Playlist extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String? description;

  @HiveField(3)
  List<String> songIds;

  @HiveField(4)
  DateTime dateCreated;

  @HiveField(5)
  DateTime dateModified;

  @HiveField(6)
  String? thumbnailUrl;

  @HiveField(7)
  bool isSystemPlaylist;

  @HiveField(8)
  int playCount;

  Playlist({
    required this.id,
    required this.name,
    this.description,
    List<String>? songIds,
    DateTime? dateCreated,
    DateTime? dateModified,
    this.thumbnailUrl,
    this.isSystemPlaylist = false,
    this.playCount = 0,
  }) : songIds = songIds ?? [],
       dateCreated = dateCreated ?? DateTime.now(),
       dateModified = dateModified ?? DateTime.now();

  // Factory constructor for system playlists
  factory Playlist.system({
    required String id,
    required String name,
    String? description,
    String? thumbnailUrl,
  }) {
    return Playlist(
      id: id,
      name: name,
      description: description,
      thumbnailUrl: thumbnailUrl,
      isSystemPlaylist: true,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'songIds': songIds,
      'dateCreated': dateCreated.toIso8601String(),
      'dateModified': dateModified.toIso8601String(),
      'thumbnailUrl': thumbnailUrl,
      'isSystemPlaylist': isSystemPlaylist,
      'playCount': playCount,
    };
  }

  // Create from JSON
  factory Playlist.fromJson(Map<String, dynamic> json) {
    return Playlist(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      songIds: List<String>.from(json['songIds'] ?? []),
      dateCreated: DateTime.parse(json['dateCreated']),
      dateModified: DateTime.parse(json['dateModified']),
      thumbnailUrl: json['thumbnailUrl'],
      isSystemPlaylist: json['isSystemPlaylist'] ?? false,
      playCount: json['playCount'] ?? 0,
    );
  }

  // Get song count
  int get songCount => songIds.length;

  // Check if playlist is empty
  bool get isEmpty => songIds.isEmpty;

  // Check if playlist is not empty
  bool get isNotEmpty => songIds.isNotEmpty;

  // Add song to playlist
  void addSong(String songId) {
    if (!songIds.contains(songId)) {
      songIds.add(songId);
      dateModified = DateTime.now();
      save(); // Save to Hive
    }
  }

  // Remove song from playlist
  void removeSong(String songId) {
    if (songIds.remove(songId)) {
      dateModified = DateTime.now();
      save(); // Save to Hive
    }
  }

  // Add multiple songs
  void addSongs(List<String> newSongIds) {
    bool changed = false;
    for (String songId in newSongIds) {
      if (!songIds.contains(songId)) {
        songIds.add(songId);
        changed = true;
      }
    }
    if (changed) {
      dateModified = DateTime.now();
      save(); // Save to Hive
    }
  }

  // Remove multiple songs
  void removeSongs(List<String> songIdsToRemove) {
    bool changed = false;
    for (String songId in songIdsToRemove) {
      if (songIds.remove(songId)) {
        changed = true;
      }
    }
    if (changed) {
      dateModified = DateTime.now();
      save(); // Save to Hive
    }
  }

  // Check if playlist contains song
  bool containsSong(String songId) {
    return songIds.contains(songId);
  }

  // Move song to different position
  void moveSong(int oldIndex, int newIndex) {
    if (oldIndex >= 0 && oldIndex < songIds.length && 
        newIndex >= 0 && newIndex < songIds.length) {
      final songId = songIds.removeAt(oldIndex);
      songIds.insert(newIndex, songId);
      dateModified = DateTime.now();
      save(); // Save to Hive
    }
  }

  // Clear all songs
  void clearSongs() {
    if (songIds.isNotEmpty) {
      songIds.clear();
      dateModified = DateTime.now();
      save(); // Save to Hive
    }
  }

  // Increment play count
  void incrementPlayCount() {
    playCount++;
    save(); // Save to Hive
  }

  // Get formatted date created
  String get formattedDateCreated {
    final now = DateTime.now();
    final difference = now.difference(dateCreated);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} year${(difference.inDays / 365).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  // Get total duration (requires song objects)
  Duration getTotalDuration(List<Song> songs) {
    Duration total = Duration.zero;
    for (String songId in songIds) {
      final song = songs.firstWhere(
        (s) => s.id == songId,
        orElse: () => Song(id: '', title: '', artist: ''),
      );
      if (song.duration != null) {
        total += song.duration!;
      }
    }
    return total;
  }

  // Get formatted total duration
  String getFormattedTotalDuration(List<Song> songs) {
    final total = getTotalDuration(songs);
    final hours = total.inHours;
    final minutes = total.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, songCount: $songCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Playlist && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method
  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? songIds,
    DateTime? dateCreated,
    DateTime? dateModified,
    String? thumbnailUrl,
    bool? isSystemPlaylist,
    int? playCount,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      songIds: songIds ?? List<String>.from(this.songIds),
      dateCreated: dateCreated ?? this.dateCreated,
      dateModified: dateModified ?? this.dateModified,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      isSystemPlaylist: isSystemPlaylist ?? this.isSystemPlaylist,
      playCount: playCount ?? this.playCount,
    );
  }
}
