import 'package:hive/hive.dart';
import 'song.dart';

part 'album.g.dart';

@HiveType(typeId: 3)
class Album extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String title;

  @HiveField(2)
  String artistId;

  @HiveField(3)
  String artistName;

  @HiveField(4)
  List<String> songIds;

  @HiveField(5)
  String? coverUrl;

  @HiveField(6)
  int? year;

  @HiveField(7)
  String? genre;

  @HiveField(8)
  DateTime dateAdded;

  @HiveField(9)
  int playCount;

  @HiveField(10)
  String? description;

  @HiveField(11)
  String? recordLabel;

  Album({
    required this.id,
    required this.title,
    required this.artistId,
    required this.artistName,
    List<String>? songIds,
    this.coverUrl,
    this.year,
    this.genre,
    DateTime? dateAdded,
    this.playCount = 0,
    this.description,
    this.recordLabel,
  }) : songIds = songIds ?? [],
       dateAdded = dateAdded ?? DateTime.now();

  // Convert to J<PERSON>N
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'artistId': artistId,
      'artistName': artistName,
      'songIds': songIds,
      'coverUrl': coverUrl,
      'year': year,
      'genre': genre,
      'dateAdded': dateAdded.toIso8601String(),
      'playCount': playCount,
      'description': description,
      'recordLabel': recordLabel,
    };
  }

  // Create from JSON
  factory Album.fromJson(Map<String, dynamic> json) {
    return Album(
      id: json['id'],
      title: json['title'],
      artistId: json['artistId'],
      artistName: json['artistName'],
      songIds: List<String>.from(json['songIds'] ?? []),
      coverUrl: json['coverUrl'],
      year: json['year'],
      genre: json['genre'],
      dateAdded: DateTime.parse(json['dateAdded']),
      playCount: json['playCount'] ?? 0,
      description: json['description'],
      recordLabel: json['recordLabel'],
    );
  }

  // Get song count
  int get songCount => songIds.length;

  // Check if album is empty
  bool get isEmpty => songIds.isEmpty;

  // Check if album is not empty
  bool get isNotEmpty => songIds.isNotEmpty;

  // Add song to album
  void addSong(String songId) {
    if (!songIds.contains(songId)) {
      songIds.add(songId);
      save(); // Save to Hive
    }
  }

  // Remove song from album
  void removeSong(String songId) {
    if (songIds.remove(songId)) {
      save(); // Save to Hive
    }
  }

  // Add multiple songs
  void addSongs(List<String> newSongIds) {
    bool changed = false;
    for (String songId in newSongIds) {
      if (!songIds.contains(songId)) {
        songIds.add(songId);
        changed = true;
      }
    }
    if (changed) {
      save(); // Save to Hive
    }
  }

  // Check if album contains song
  bool containsSong(String songId) {
    return songIds.contains(songId);
  }

  // Increment play count
  void incrementPlayCount() {
    playCount++;
    save(); // Save to Hive
  }

  // Get total duration (requires song objects)
  Duration getTotalDuration(List<Song> songs) {
    Duration total = Duration.zero;
    for (String songId in songIds) {
      final song = songs.firstWhere(
        (s) => s.id == songId,
        orElse: () => Song(id: '', title: '', artist: ''),
      );
      if (song.duration != null) {
        total += song.duration!;
      }
    }
    return total;
  }

  // Get formatted total duration
  String getFormattedTotalDuration(List<Song> songs) {
    final total = getTotalDuration(songs);
    final hours = total.inHours;
    final minutes = total.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  // Get formatted play count
  String get formattedPlayCount {
    if (playCount >= 1000000) {
      return '${(playCount / 1000000).toStringAsFixed(1)}M plays';
    } else if (playCount >= 1000) {
      return '${(playCount / 1000).toStringAsFixed(1)}K plays';
    } else {
      return '$playCount plays';
    }
  }

  // Get display name (title by artist)
  String get displayName => '$title by $artistName';

  // Get year string
  String get yearString => year?.toString() ?? 'Unknown';

  // Check if album is recent (within last year)
  bool get isRecent {
    if (year == null) return false;
    final currentYear = DateTime.now().year;
    return currentYear - year! <= 1;
  }

  // Get age in years
  int? get ageInYears {
    if (year == null) return null;
    return DateTime.now().year - year!;
  }

  @override
  String toString() {
    return 'Album(id: $id, title: $title, artist: $artistName, songCount: $songCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Album && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method
  Album copyWith({
    String? id,
    String? title,
    String? artistId,
    String? artistName,
    List<String>? songIds,
    String? coverUrl,
    int? year,
    String? genre,
    DateTime? dateAdded,
    int? playCount,
    String? description,
    String? recordLabel,
  }) {
    return Album(
      id: id ?? this.id,
      title: title ?? this.title,
      artistId: artistId ?? this.artistId,
      artistName: artistName ?? this.artistName,
      songIds: songIds ?? List<String>.from(this.songIds),
      coverUrl: coverUrl ?? this.coverUrl,
      year: year ?? this.year,
      genre: genre ?? this.genre,
      dateAdded: dateAdded ?? this.dateAdded,
      playCount: playCount ?? this.playCount,
      description: description ?? this.description,
      recordLabel: recordLabel ?? this.recordLabel,
    );
  }
}
