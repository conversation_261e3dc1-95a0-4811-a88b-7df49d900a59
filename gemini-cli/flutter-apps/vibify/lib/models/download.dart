import 'package:hive/hive.dart';

part 'download.g.dart';

@HiveType(typeId: 4)
enum DownloadStatus {
  @HiveField(0)
  pending,
  @HiveField(1)
  downloading,
  @HiveField(2)
  completed,
  @HiveField(3)
  failed,
  @HiveField(4)
  cancelled,
  @HiveField(5)
  paused,
}

@HiveType(typeId: 5)
class Download extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String songId;

  @HiveField(2)
  String title;

  @HiveField(3)
  String artist;

  @HiveField(4)
  String youtubeUrl;

  @HiveField(5)
  String? localPath;

  @HiveField(6)
  DownloadStatus status;

  @HiveField(7)
  double progress;

  @HiveField(8)
  int? totalBytes;

  @HiveField(9)
  int? downloadedBytes;

  @HiveField(10)
  DateTime dateStarted;

  @HiveField(11)
  DateTime? dateCompleted;

  @HiveField(12)
  String? errorMessage;

  @HiveField(13)
  String? thumbnailUrl;

  @HiveField(14)
  Duration? duration;

  @HiveField(15)
  String? taskId; // For flutter_downloader

  Download({
    required this.id,
    required this.songId,
    required this.title,
    required this.artist,
    required this.youtubeUrl,
    this.localPath,
    this.status = DownloadStatus.pending,
    this.progress = 0.0,
    this.totalBytes,
    this.downloadedBytes,
    DateTime? dateStarted,
    this.dateCompleted,
    this.errorMessage,
    this.thumbnailUrl,
    this.duration,
    this.taskId,
  }) : dateStarted = dateStarted ?? DateTime.now();

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'songId': songId,
      'title': title,
      'artist': artist,
      'youtubeUrl': youtubeUrl,
      'localPath': localPath,
      'status': status.index,
      'progress': progress,
      'totalBytes': totalBytes,
      'downloadedBytes': downloadedBytes,
      'dateStarted': dateStarted.toIso8601String(),
      'dateCompleted': dateCompleted?.toIso8601String(),
      'errorMessage': errorMessage,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration?.inMilliseconds,
      'taskId': taskId,
    };
  }

  // Create from JSON
  factory Download.fromJson(Map<String, dynamic> json) {
    return Download(
      id: json['id'],
      songId: json['songId'],
      title: json['title'],
      artist: json['artist'],
      youtubeUrl: json['youtubeUrl'],
      localPath: json['localPath'],
      status: DownloadStatus.values[json['status'] ?? 0],
      progress: (json['progress'] ?? 0.0).toDouble(),
      totalBytes: json['totalBytes'],
      downloadedBytes: json['downloadedBytes'],
      dateStarted: DateTime.parse(json['dateStarted']),
      dateCompleted: json['dateCompleted'] != null
          ? DateTime.parse(json['dateCompleted'])
          : null,
      errorMessage: json['errorMessage'],
      thumbnailUrl: json['thumbnailUrl'],
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'])
          : null,
      taskId: json['taskId'],
    );
  }

  // Update download progress
  void updateProgress(double newProgress, {int? downloaded, int? total}) {
    progress = newProgress.clamp(0.0, 1.0);
    if (downloaded != null) downloadedBytes = downloaded;
    if (total != null) totalBytes = total;

    if (progress >= 1.0 && status == DownloadStatus.downloading) {
      status = DownloadStatus.completed;
      dateCompleted = DateTime.now();
    }

    save(); // Save to Hive
  }

  // Mark as started
  void markAsStarted({String? newTaskId}) {
    status = DownloadStatus.downloading;
    if (newTaskId != null) taskId = newTaskId;
    save(); // Save to Hive
  }

  // Mark as completed
  void markAsCompleted(String filePath) {
    status = DownloadStatus.completed;
    localPath = filePath;
    progress = 1.0;
    dateCompleted = DateTime.now();
    save(); // Save to Hive
  }

  // Mark as failed
  void markAsFailed(String error) {
    status = DownloadStatus.failed;
    errorMessage = error;
    save(); // Save to Hive
  }

  // Mark as cancelled
  void markAsCancelled() {
    status = DownloadStatus.cancelled;
    save(); // Save to Hive
  }

  // Mark as paused
  void markAsPaused() {
    status = DownloadStatus.paused;
    save(); // Save to Hive
  }

  // Resume download
  void resume() {
    if (status == DownloadStatus.paused) {
      status = DownloadStatus.pending;
      save(); // Save to Hive
    }
  }

  // Get progress percentage
  int get progressPercentage => (progress * 100).round();

  // Get formatted file size
  String get formattedFileSize {
    if (totalBytes == null) return 'Unknown size';
    final mb = totalBytes! / (1024 * 1024);
    return '${mb.toStringAsFixed(1)} MB';
  }

  // Get formatted downloaded size
  String get formattedDownloadedSize {
    if (downloadedBytes == null) return '0 MB';
    final mb = downloadedBytes! / (1024 * 1024);
    return '${mb.toStringAsFixed(1)} MB';
  }

  // Get download speed (requires time tracking)
  String getDownloadSpeed() {
    if (downloadedBytes == null || status != DownloadStatus.downloading) {
      return '0 KB/s';
    }

    final elapsed = DateTime.now().difference(dateStarted);
    if (elapsed.inSeconds == 0) return '0 KB/s';

    final bytesPerSecond = downloadedBytes! / elapsed.inSeconds;
    if (bytesPerSecond >= 1024 * 1024) {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    } else if (bytesPerSecond >= 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${bytesPerSecond.toStringAsFixed(0)} B/s';
    }
  }

  // Get estimated time remaining
  String getEstimatedTimeRemaining() {
    if (downloadedBytes == null ||
        totalBytes == null ||
        downloadedBytes! == 0 ||
        status != DownloadStatus.downloading) {
      return 'Unknown';
    }

    final elapsed = DateTime.now().difference(dateStarted);
    final bytesPerSecond = downloadedBytes! / elapsed.inSeconds;
    final remainingBytes = totalBytes! - downloadedBytes!;
    final remainingSeconds = remainingBytes / bytesPerSecond;

    if (remainingSeconds > 3600) {
      return '${(remainingSeconds / 3600).toStringAsFixed(0)}h';
    } else if (remainingSeconds > 60) {
      return '${(remainingSeconds / 60).toStringAsFixed(0)}m';
    } else {
      return '${remainingSeconds.toStringAsFixed(0)}s';
    }
  }

  // Check if download is active
  bool get isActive =>
      status == DownloadStatus.downloading || status == DownloadStatus.pending;

  // Check if download is completed
  bool get isCompleted => status == DownloadStatus.completed;

  // Check if download failed
  bool get isFailed => status == DownloadStatus.failed;

  // Check if download is paused
  bool get isPaused => status == DownloadStatus.paused;

  // Check if download can be retried
  bool get canRetry =>
      status == DownloadStatus.failed || status == DownloadStatus.cancelled;

  // Get status display text
  String get statusText {
    switch (status) {
      case DownloadStatus.pending:
        return 'Pending';
      case DownloadStatus.downloading:
        return 'Downloading';
      case DownloadStatus.completed:
        return 'Completed';
      case DownloadStatus.failed:
        return 'Failed';
      case DownloadStatus.cancelled:
        return 'Cancelled';
      case DownloadStatus.paused:
        return 'Paused';
    }
  }

  @override
  String toString() {
    return 'Download(id: $id, title: $title, status: $statusText, progress: $progressPercentage%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Download && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method
  Download copyWith({
    String? id,
    String? songId,
    String? title,
    String? artist,
    String? youtubeUrl,
    String? localPath,
    DownloadStatus? status,
    double? progress,
    int? totalBytes,
    int? downloadedBytes,
    DateTime? dateStarted,
    DateTime? dateCompleted,
    String? errorMessage,
    String? thumbnailUrl,
    Duration? duration,
    String? taskId,
  }) {
    return Download(
      id: id ?? this.id,
      songId: songId ?? this.songId,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      youtubeUrl: youtubeUrl ?? this.youtubeUrl,
      localPath: localPath ?? this.localPath,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      totalBytes: totalBytes ?? this.totalBytes,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
      dateStarted: dateStarted ?? this.dateStarted,
      dateCompleted: dateCompleted ?? this.dateCompleted,
      errorMessage: errorMessage ?? this.errorMessage,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      duration: duration ?? this.duration,
      taskId: taskId ?? this.taskId,
    );
  }
}
